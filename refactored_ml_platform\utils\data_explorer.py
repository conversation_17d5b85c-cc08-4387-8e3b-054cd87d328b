#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据探索工具
提供数据探索和统计分析功能
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.stats import chi2_contingency
import logging
from typing import Dict, List, Tuple, Optional, Any
from pathlib import Path

from .error_handler import get_error_handler, error_handler


class DataExplorer:
    """数据探索器"""
    
    def __init__(self):
        """初始化数据探索器"""
        self.logger = logging.getLogger(__name__)
        self.error_handler = get_error_handler()
        
        # 设置matplotlib中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 设置seaborn样式
        sns.set_style("whitegrid")
        sns.set_palette("husl")
    
    @error_handler("数据基本信息分析")
    def analyze_basic_info(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        分析数据基本信息
        
        Args:
            df: 数据框
            
        Returns:
            基本信息字典
        """
        info = {
            'shape': df.shape,
            'columns': list(df.columns),
            'dtypes': df.dtypes.to_dict(),
            'memory_usage': df.memory_usage(deep=True).sum() / 1024**2,  # MB
            'missing_values': df.isnull().sum().to_dict(),
            'missing_percentage': (df.isnull().sum() / len(df) * 100).to_dict(),
            'duplicates': df.duplicated().sum(),
            'numeric_columns': df.select_dtypes(include=[np.number]).columns.tolist(),
            'categorical_columns': df.select_dtypes(include=['object', 'category']).columns.tolist()
        }
        
        self.logger.info(f"数据基本信息分析完成，形状: {info['shape']}")
        return info
    
    @error_handler("描述性统计分析")
    def analyze_descriptive_stats(self, df: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """
        分析描述性统计
        
        Args:
            df: 数据框
            
        Returns:
            描述性统计结果
        """
        results = {}
        
        # 数值变量描述性统计
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        if len(numeric_cols) > 0:
            results['numeric'] = df[numeric_cols].describe()
            
            # 添加偏度和峰度
            skewness = df[numeric_cols].skew()
            kurtosis = df[numeric_cols].kurtosis()
            
            results['numeric'].loc['skewness'] = skewness
            results['numeric'].loc['kurtosis'] = kurtosis
        
        # 分类变量描述性统计
        categorical_cols = df.select_dtypes(include=['object', 'category']).columns
        if len(categorical_cols) > 0:
            cat_stats = []
            for col in categorical_cols:
                stats_dict = {
                    'column': col,
                    'unique_count': df[col].nunique(),
                    'most_frequent': df[col].mode().iloc[0] if not df[col].mode().empty else None,
                    'most_frequent_count': df[col].value_counts().iloc[0] if len(df[col].value_counts()) > 0 else 0,
                    'missing_count': df[col].isnull().sum()
                }
                cat_stats.append(stats_dict)
            
            results['categorical'] = pd.DataFrame(cat_stats)
        
        self.logger.info("描述性统计分析完成")
        return results
    
    @error_handler("相关性分析")
    def analyze_correlation(self, df: pd.DataFrame, method: str = 'pearson') -> pd.DataFrame:
        """
        分析变量间相关性
        
        Args:
            df: 数据框
            method: 相关性方法 ('pearson', 'spearman', 'kendall')
            
        Returns:
            相关性矩阵
        """
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        
        if len(numeric_cols) < 2:
            self.logger.warning("数值变量少于2个，无法进行相关性分析")
            return pd.DataFrame()
        
        correlation_matrix = df[numeric_cols].corr(method=method)
        
        self.logger.info(f"相关性分析完成，方法: {method}")
        return correlation_matrix
    
    @error_handler("分组概率分析")
    def analyze_group_probabilities(self, df: pd.DataFrame, target_col: str, 
                                  group_cols: List[str], bins: int = 5) -> Dict[str, pd.DataFrame]:
        """
        分析分组概率
        
        Args:
            df: 数据框
            target_col: 目标变量列名
            group_cols: 分组变量列名列表
            bins: 连续变量分箱数量
            
        Returns:
            分组概率分析结果
        """
        results = {}
        
        for col in group_cols:
            if col not in df.columns:
                continue
            
            try:
                # 处理连续变量
                if df[col].dtype in ['int64', 'float64']:
                    # 分箱处理
                    df_temp = df.copy()
                    df_temp[f'{col}_binned'] = pd.cut(df_temp[col], bins=bins, duplicates='drop')
                    group_col = f'{col}_binned'
                else:
                    df_temp = df.copy()
                    group_col = col
                
                # 计算分组概率
                crosstab = pd.crosstab(df_temp[group_col], df_temp[target_col], normalize='index')
                
                # 添加计数信息
                counts = pd.crosstab(df_temp[group_col], df_temp[target_col])
                
                # 合并概率和计数
                result_df = pd.DataFrame()
                for target_val in crosstab.columns:
                    result_df[f'prob_{target_val}'] = crosstab[target_val]
                    result_df[f'count_{target_val}'] = counts[target_val]
                
                result_df['total_count'] = counts.sum(axis=1)
                
                results[col] = result_df
                
            except Exception as e:
                self.logger.warning(f"分析变量 {col} 的分组概率时出错: {e}")
                continue
        
        self.logger.info(f"分组概率分析完成，分析了 {len(results)} 个变量")
        return results
    
    @error_handler("卡方检验")
    def perform_chi_square_test(self, df: pd.DataFrame, col1: str, col2: str) -> Dict[str, Any]:
        """
        执行卡方检验
        
        Args:
            df: 数据框
            col1: 第一个变量
            col2: 第二个变量
            
        Returns:
            卡方检验结果
        """
        # 创建交叉表
        crosstab = pd.crosstab(df[col1], df[col2])
        
        # 执行卡方检验
        chi2, p_value, dof, expected = chi2_contingency(crosstab)
        
        # 计算Cramer's V
        n = crosstab.sum().sum()
        cramers_v = np.sqrt(chi2 / (n * (min(crosstab.shape) - 1)))
        
        result = {
            'chi2_statistic': chi2,
            'p_value': p_value,
            'degrees_of_freedom': dof,
            'cramers_v': cramers_v,
            'crosstab': crosstab,
            'expected_frequencies': pd.DataFrame(expected, 
                                               index=crosstab.index, 
                                               columns=crosstab.columns)
        }
        
        self.logger.info(f"卡方检验完成: {col1} vs {col2}, p-value: {p_value:.4f}")
        return result
    
    @error_handler("创建相关性热力图")
    def create_correlation_heatmap(self, correlation_matrix: pd.DataFrame, 
                                 save_path: Optional[str] = None) -> plt.Figure:
        """
        创建相关性热力图
        
        Args:
            correlation_matrix: 相关性矩阵
            save_path: 保存路径
            
        Returns:
            matplotlib图形对象
        """
        fig, ax = plt.subplots(figsize=(10, 8))
        
        # 创建热力图
        mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))
        sns.heatmap(correlation_matrix, mask=mask, annot=True, cmap='coolwarm', 
                   center=0, square=True, linewidths=0.5, ax=ax)
        
        plt.title('变量相关性热力图', fontsize=16, pad=20)
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            self.logger.info(f"相关性热力图已保存到: {save_path}")
        
        return fig
    
    @error_handler("创建分布图")
    def create_distribution_plot(self, df: pd.DataFrame, column: str, 
                               save_path: Optional[str] = None) -> plt.Figure:
        """
        创建变量分布图
        
        Args:
            df: 数据框
            column: 列名
            save_path: 保存路径
            
        Returns:
            matplotlib图形对象
        """
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        
        # 直方图
        axes[0, 0].hist(df[column].dropna(), bins=30, alpha=0.7, color='skyblue', edgecolor='black')
        axes[0, 0].set_title(f'{column} - 直方图')
        axes[0, 0].set_xlabel(column)
        axes[0, 0].set_ylabel('频数')
        
        # 箱线图
        axes[0, 1].boxplot(df[column].dropna())
        axes[0, 1].set_title(f'{column} - 箱线图')
        axes[0, 1].set_ylabel(column)
        
        # Q-Q图
        stats.probplot(df[column].dropna(), dist="norm", plot=axes[1, 0])
        axes[1, 0].set_title(f'{column} - Q-Q图')
        
        # 密度图
        df[column].dropna().plot.density(ax=axes[1, 1], color='orange')
        axes[1, 1].set_title(f'{column} - 密度图')
        axes[1, 1].set_xlabel(column)
        axes[1, 1].set_ylabel('密度')
        
        plt.suptitle(f'{column} 分布分析', fontsize=16)
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            self.logger.info(f"分布图已保存到: {save_path}")
        
        return fig
    
    @error_handler("创建分组概率图")
    def create_group_probability_plot(self, group_prob_result: pd.DataFrame, 
                                    variable_name: str, target_name: str,
                                    save_path: Optional[str] = None) -> plt.Figure:
        """
        创建分组概率图
        
        Args:
            group_prob_result: 分组概率结果
            variable_name: 变量名
            target_name: 目标变量名
            save_path: 保存路径
            
        Returns:
            matplotlib图形对象
        """
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 概率图
        prob_cols = [col for col in group_prob_result.columns if col.startswith('prob_')]
        group_prob_result[prob_cols].plot(kind='bar', ax=ax1, width=0.8)
        ax1.set_title(f'{variable_name} 各组的 {target_name} 概率分布')
        ax1.set_xlabel(variable_name)
        ax1.set_ylabel('概率')
        ax1.legend(title=target_name)
        ax1.tick_params(axis='x', rotation=45)
        
        # 计数图
        count_cols = [col for col in group_prob_result.columns if col.startswith('count_')]
        group_prob_result[count_cols].plot(kind='bar', ax=ax2, width=0.8)
        ax2.set_title(f'{variable_name} 各组的 {target_name} 计数分布')
        ax2.set_xlabel(variable_name)
        ax2.set_ylabel('计数')
        ax2.legend(title=target_name)
        ax2.tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            self.logger.info(f"分组概率图已保存到: {save_path}")
        
        return fig
