#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据管理主模块
整合数据加载、预览、验证、预处理等功能
"""

import tkinter as tk
from tkinter import ttk
from typing import Optional, Dict, Any
import pandas as pd
from sklearn.model_selection import train_test_split

from ...core.base_gui import BaseGUI
from ...core.component_factory import get_component_factory
from ...core.event_manager import EventTypes
from ...components.data_widgets import FileSelector, DataPreviewWidget
from ...components.progress_widgets import ProgressWidget, StatusIndicator


class DataManagementModule(BaseGUI):
    """
    数据管理模块主类
    提供完整的数据管理功能界面
    """
    
    def __init__(self, parent: tk.Widget):
        """初始化数据管理模块"""
        self.current_data: Optional[pd.DataFrame] = None
        self.data_file_path: Optional[str] = None
        
        super().__init__(parent)
        
        # 订阅相关事件
        self._bind_events()
    
    def _setup_ui(self):
        """设置UI界面"""
        factory = get_component_factory()
        
        # 主框架
        self.main_frame = factory.create_frame(self.parent)
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建标签页容器
        self.notebook = factory.create_notebook(self.main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        self.register_component('notebook', self.notebook)
        
        # 数据加载标签页
        self._create_data_loader_tab()
        
        # 数据预览标签页
        self._create_data_preview_tab()
        
        # 数据验证标签页
        self._create_data_validation_tab()
        
        # 数据预处理标签页
        self._create_data_preprocessing_tab()
    
    def _create_data_loader_tab(self):
        """创建数据加载标签页"""
        factory = get_component_factory()
        
        # 创建标签页框架
        loader_frame = factory.create_frame(self.notebook)
        self.notebook.add(loader_frame, text="数据加载")
        self.register_component('loader_frame', loader_frame)
        
        # 标题
        title_frame = factory.create_frame(loader_frame)
        title_frame.pack(fill=tk.X, padx=10, pady=10)
        
        factory.create_label(title_frame, text="数据文件加载", style='title').pack(anchor=tk.W)
        factory.create_label(title_frame, text="支持CSV格式文件，目标变量应命名为'label'或'target'", 
                           style='secondary').pack(anchor=tk.W, pady=(5, 0))
        
        # 文件选择器
        self.file_selector = FileSelector(
            loader_frame,
            title="选择数据文件",
            filetypes=[("CSV文件", "*.csv"), ("所有文件", "*.*")],
            on_file_selected=self._on_file_selected
        )
        
        # 状态指示器
        status_frame = factory.create_frame(loader_frame)
        status_frame.pack(fill=tk.X, padx=10, pady=(10, 0))
        
        self.status_indicator = StatusIndicator(status_frame)
        
        # 进度条
        self.progress_widget = ProgressWidget(loader_frame, show_percentage=True)
        
        # 加载按钮
        button_frame = factory.create_frame(loader_frame)
        button_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.load_button = factory.create_button(
            button_frame, 
            text="加载数据", 
            command=self._load_data,
            style='primary'
        )
        self.load_button.pack(side=tk.LEFT)
        self.load_button.config(state=tk.DISABLED)
        self.register_component('load_button', self.load_button)
        
        # 重新加载按钮
        reload_button = factory.create_button(
            button_frame,
            text="重新加载",
            command=self._reload_data,
            style='secondary'
        )
        reload_button.pack(side=tk.LEFT, padx=(10, 0))
        reload_button.config(state=tk.DISABLED)
        self.register_component('reload_button', reload_button)
    
    def _create_data_preview_tab(self):
        """创建数据预览标签页"""
        factory = get_component_factory()
        
        # 创建标签页框架
        preview_frame = factory.create_frame(self.notebook)
        self.notebook.add(preview_frame, text="数据预览")
        self.register_component('preview_frame', preview_frame)
        
        # 数据预览组件
        self.data_preview = DataPreviewWidget(preview_frame)
    
    def _create_data_validation_tab(self):
        """创建数据验证标签页"""
        factory = get_component_factory()
        
        # 创建标签页框架
        validation_frame = factory.create_frame(self.notebook)
        self.notebook.add(validation_frame, text="数据验证")
        self.register_component('validation_frame', validation_frame)
        
        # 标题
        title_frame = factory.create_frame(validation_frame)
        title_frame.pack(fill=tk.X, padx=10, pady=10)
        
        factory.create_label(title_frame, text="数据质量验证", style='title').pack(anchor=tk.W)
        
        # 验证结果显示区域
        result_frame = factory.create_frame(validation_frame, style='card')
        result_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))
        
        # 验证结果文本框
        self.validation_text = factory.create_text(result_frame, height=20, state=tk.DISABLED)
        self.validation_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        self.register_component('validation_text', self.validation_text)
        
        # 验证按钮
        validate_button = factory.create_button(
            validation_frame,
            text="开始验证",
            command=self._validate_data,
            style='primary'
        )
        validate_button.pack(pady=(0, 10))
        validate_button.config(state=tk.DISABLED)
        self.register_component('validate_button', validate_button)
    
    def _create_data_preprocessing_tab(self):
        """创建数据预处理标签页"""
        factory = get_component_factory()
        
        # 创建标签页框架
        preprocessing_frame = factory.create_frame(self.notebook)
        self.notebook.add(preprocessing_frame, text="数据预处理")
        self.register_component('preprocessing_frame', preprocessing_frame)
        
        # 标题
        title_frame = factory.create_frame(preprocessing_frame)
        title_frame.pack(fill=tk.X, padx=10, pady=10)
        
        factory.create_label(title_frame, text="数据预处理配置", style='title').pack(anchor=tk.W)
        
        # 预处理选项
        options_frame = factory.create_frame(preprocessing_frame, style='card')
        options_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        # 缺失值处理
        missing_frame = factory.create_frame(options_frame)
        missing_frame.pack(fill=tk.X, padx=10, pady=10)
        
        factory.create_label(missing_frame, text="缺失值处理:", style='default').pack(anchor=tk.W)
        
        self.missing_method_var = tk.StringVar(value="drop")
        self.register_variable('missing_method', self.missing_method_var)
        
        missing_options = [
            ("删除含缺失值的行", "drop"),
            ("均值填充", "mean"),
            ("中位数填充", "median"),
            ("众数填充", "mode")
        ]
        
        for text, value in missing_options:
            radio = factory.create_radiobutton(missing_frame, text=text, 
                                             variable=self.missing_method_var, value=value)
            radio.pack(anchor=tk.W, padx=20)
        
        # 特征缩放
        scaling_frame = factory.create_frame(options_frame)
        scaling_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        factory.create_label(scaling_frame, text="特征缩放:", style='default').pack(anchor=tk.W)
        
        self.scaling_method_var = tk.StringVar(value="standard")
        self.register_variable('scaling_method', self.scaling_method_var)
        
        scaling_options = [
            ("标准化 (StandardScaler)", "standard"),
            ("最小-最大缩放 (MinMaxScaler)", "minmax"),
            ("不进行缩放", "none")
        ]
        
        for text, value in scaling_options:
            radio = factory.create_radiobutton(scaling_frame, text=text,
                                             variable=self.scaling_method_var, value=value)
            radio.pack(anchor=tk.W, padx=20)
        
        # 预处理按钮
        preprocess_button = factory.create_button(
            preprocessing_frame,
            text="应用预处理",
            command=self._apply_preprocessing,
            style='primary'
        )
        preprocess_button.pack(pady=(0, 10))
        preprocess_button.config(state=tk.DISABLED)
        self.register_component('preprocess_button', preprocess_button)
    
    def _bind_events(self):
        """绑定事件"""
        # 订阅数据加载完成事件
        self.subscribe_event(EventTypes.DATA_LOADED, self._on_data_loaded)
        self.subscribe_event(EventTypes.DATA_PREPROCESSED, self._on_data_preprocessed)
    
    def _on_file_selected(self, file_path: str):
        """文件选择回调"""
        self.data_file_path = file_path
        self.load_button.config(state=tk.NORMAL)
        self.status_indicator.set_status("idle", f"已选择文件: {file_path}")
    
    def _load_data(self):
        """加载数据"""
        if not self.data_file_path:
            self.show_error("错误", "请先选择数据文件")
            return
        
        try:
            self.status_indicator.set_status("running", "正在加载数据...")
            self.progress_widget.set_progress(20, "读取文件...")
            
            # 加载CSV文件
            self.current_data = pd.read_csv(self.data_file_path, encoding='utf-8-sig')
            self.progress_widget.set_progress(60, "验证数据格式...")
            
            # 基本验证
            if self.current_data.empty:
                raise ValueError("数据文件为空")
            
            self.progress_widget.set_progress(80, "更新界面...")
            
            # 更新预览
            self.data_preview.load_data(self.current_data)
            
            # 启用其他功能按钮
            self._enable_data_dependent_buttons()
            
            self.progress_widget.complete("数据加载完成")
            self.status_indicator.set_status("success", 
                                           f"成功加载 {len(self.current_data)} 行 {len(self.current_data.columns)} 列")
            
            # 发布事件
            self.publish_event(EventTypes.DATA_LOADED, {
                'data': self.current_data,
                'file_path': self.data_file_path
            })
            
        except Exception as e:
            self.progress_widget.reset()
            self.status_indicator.set_status("error", f"加载失败: {str(e)}")
            self.show_error("加载失败", f"无法加载数据文件:\n{str(e)}")
    
    def _reload_data(self):
        """重新加载数据"""
        if self.data_file_path:
            self._load_data()
    
    def _validate_data(self):
        """验证数据"""
        if self.current_data is None:
            self.show_warning("警告", "请先加载数据")
            return
        
        try:
            validation_results = []
            
            # 基本信息
            validation_results.append("=== 数据基本信息 ===")
            validation_results.append(f"数据形状: {self.current_data.shape}")
            validation_results.append(f"内存使用: {self.current_data.memory_usage(deep=True).sum() / 1024 / 1024:.2f} MB")
            validation_results.append("")
            
            # 缺失值检查
            validation_results.append("=== 缺失值检查 ===")
            missing_info = self.current_data.isnull().sum()
            missing_cols = missing_info[missing_info > 0]
            
            if len(missing_cols) == 0:
                validation_results.append("✓ 无缺失值")
            else:
                validation_results.append("⚠ 发现缺失值:")
                for col, count in missing_cols.items():
                    pct = (count / len(self.current_data)) * 100
                    validation_results.append(f"  {col}: {count} ({pct:.1f}%)")
            
            validation_results.append("")
            
            # 数据类型检查
            validation_results.append("=== 数据类型检查 ===")
            for col in self.current_data.columns:
                dtype = str(self.current_data[col].dtype)
                validation_results.append(f"{col}: {dtype}")
            
            validation_results.append("")
            
            # 目标变量检查
            validation_results.append("=== 目标变量检查 ===")
            target_cols = [col for col in self.current_data.columns 
                          if col.lower() in ['label', 'target', 'y']]
            
            if target_cols:
                target_col = target_cols[0]
                validation_results.append(f"✓ 找到目标变量: {target_col}")
                
                # 检查目标变量分布
                value_counts = self.current_data[target_col].value_counts()
                validation_results.append("目标变量分布:")
                for value, count in value_counts.items():
                    pct = (count / len(self.current_data)) * 100
                    validation_results.append(f"  {value}: {count} ({pct:.1f}%)")
            else:
                validation_results.append("⚠ 未找到标准目标变量列 (label/target/y)")
                validation_results.append("请确保目标变量列命名正确")
            
            # 显示验证结果
            self.validation_text.config(state=tk.NORMAL)
            self.validation_text.delete(1.0, tk.END)
            self.validation_text.insert(tk.END, "\n".join(validation_results))
            self.validation_text.config(state=tk.DISABLED)
            
            self.publish_event(EventTypes.DATA_VALIDATED, {
                'data': self.current_data,
                'validation_results': validation_results
            })
            
        except Exception as e:
            self.show_error("验证失败", f"数据验证过程中出现错误:\n{str(e)}")
    
    def _apply_preprocessing(self):
        """应用数据预处理"""
        if self.current_data is None:
            self.show_warning("警告", "请先加载数据")
            return
        
        try:
            processed_data = self.current_data.copy()
            
            # 缺失值处理
            missing_method = self.missing_method_var.get()
            if missing_method == "drop":
                processed_data = processed_data.dropna()
            elif missing_method == "mean":
                numeric_cols = processed_data.select_dtypes(include=['number']).columns
                processed_data[numeric_cols] = processed_data[numeric_cols].fillna(
                    processed_data[numeric_cols].mean())
            elif missing_method == "median":
                numeric_cols = processed_data.select_dtypes(include=['number']).columns
                processed_data[numeric_cols] = processed_data[numeric_cols].fillna(
                    processed_data[numeric_cols].median())
            elif missing_method == "mode":
                for col in processed_data.columns:
                    processed_data[col] = processed_data[col].fillna(processed_data[col].mode()[0])
            
            # 特征缩放 (这里只是示例，实际应用中需要更复杂的处理)
            scaling_method = self.scaling_method_var.get()
            if scaling_method != "none":
                # 这里应该集成实际的预处理逻辑
                pass
            
            # 更新数据
            self.current_data = processed_data
            self.data_preview.load_data(self.current_data)
            
            # 数据分割 - 假设最后一列是目标变量
            if len(self.current_data.columns) > 1:
                # 分离特征和目标变量
                X = self.current_data.iloc[:, :-1]  # 所有列除了最后一列
                y = self.current_data.iloc[:, -1]   # 最后一列作为目标变量
                
                # 分割训练和测试集
                X_train, X_test, y_train, y_test = train_test_split(
                    X, y, test_size=0.2, random_state=42
                )
                
                self.show_info("成功", f"数据预处理完成\n训练集: {X_train.shape[0]} 行\n测试集: {X_test.shape[0]} 行")
                
                # 发布包含分割数据的事件
                self.publish_event(EventTypes.DATA_PREPROCESSED, {
                    'data': self.current_data,
                    'X_train': X_train,
                    'X_test': X_test,
                    'y_train': y_train,
                    'y_test': y_test,
                    'feature_names': list(X.columns),
                    'preprocessing_config': {
                        'missing_method': missing_method,
                        'scaling_method': scaling_method
                    }
                })
            else:
                self.show_info("成功", "数据预处理完成")
                # 如果只有一列数据，无法进行分割
                self.publish_event(EventTypes.DATA_PREPROCESSED, {
                    'data': self.current_data,
                    'preprocessing_config': {
                        'missing_method': missing_method,
                        'scaling_method': scaling_method
                    }
                })
            
        except Exception as e:
            self.show_error("预处理失败", f"数据预处理过程中出现错误:\n{str(e)}")
    
    def _enable_data_dependent_buttons(self):
        """启用依赖数据的按钮"""
        buttons = ['reload_button', 'validate_button', 'preprocess_button']
        for button_name in buttons:
            button = self.get_component(button_name)
            if button:
                button.config(state=tk.NORMAL)
    
    def _on_data_loaded(self, data):
        """数据加载完成事件处理"""
        pass
    
    def _on_data_preprocessed(self, data):
        """数据预处理完成事件处理"""
        pass
    
    def get_current_data(self) -> Optional[pd.DataFrame]:
        """获取当前数据"""
        return self.current_data
    
    def get_data_file_path(self) -> Optional[str]:
        """获取数据文件路径"""
        return self.data_file_path
