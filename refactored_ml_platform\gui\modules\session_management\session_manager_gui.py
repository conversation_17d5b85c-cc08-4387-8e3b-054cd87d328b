#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
会话管理GUI模块
提供训练会话的图形化管理界面
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
from datetime import datetime
from typing import Dict, List, Optional, Any
import threading

from ....core.session_manager import get_session_manager, TrainingSession
from ....core.event_manager import get_event_manager
from ....utils.error_handler import get_error_handler


class SessionManagerGUI:
    """会话管理GUI"""
    
    def __init__(self, parent):
        """初始化会话管理GUI"""
        self.parent = parent
        self.session_manager = get_session_manager()
        self.event_manager = get_event_manager()
        self.error_handler = get_error_handler()
        
        # GUI组件
        self.frame = None
        self.sessions_tree = None
        self.detail_text = None
        
        # 控制变量
        self.selected_session_id = None
        self.status_var = tk.StringVar(value="就绪")
        
        # 创建界面
        self._create_interface()
        
        # 注册事件监听
        self._register_events()
        
        # 初始加载会话列表
        self._refresh_sessions()
    
    def _create_interface(self):
        """创建界面"""
        # 主框架
        self.frame = ttk.Frame(self.parent)
        self.frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 标题
        title_frame = ttk.Frame(self.frame)
        title_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(title_frame, text="训练会话管理", font=('Arial', 16, 'bold')).pack(side=tk.LEFT)
        
        # 工具栏
        self._create_toolbar()
        
        # 主要内容区域
        content_frame = ttk.Frame(self.frame)
        content_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # 左侧：会话列表
        self._create_sessions_list(content_frame)
        
        # 右侧：会话详情
        self._create_session_details(content_frame)
        
        # 状态栏
        self._create_status_bar()
    
    def _create_toolbar(self):
        """创建工具栏"""
        toolbar_frame = ttk.Frame(self.frame)
        toolbar_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 会话操作按钮
        ttk.Button(toolbar_frame, text="📝 新建会话", command=self._new_session).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar_frame, text="🔄 激活会话", command=self._activate_session).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar_frame, text="💾 保存会话", command=self._save_session).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar_frame, text="📂 加载会话", command=self._load_session).pack(side=tk.LEFT, padx=(0, 5))
        
        # 分隔符
        ttk.Separator(toolbar_frame, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=10)
        
        # 管理操作按钮
        ttk.Button(toolbar_frame, text="📊 导出报告", command=self._export_report).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar_frame, text="🗑️ 删除会话", command=self._delete_session).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar_frame, text="🔄 刷新列表", command=self._refresh_sessions).pack(side=tk.LEFT, padx=(0, 5))
        
        # 右侧：统计信息
        ttk.Button(toolbar_frame, text="📈 统计信息", command=self._show_statistics).pack(side=tk.RIGHT)
    
    def _create_sessions_list(self, parent):
        """创建会话列表"""
        # 左侧框架
        left_frame = ttk.LabelFrame(parent, text="会话列表")
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        # 会话列表
        columns = ('会话ID', '会话名称', '状态', '创建时间', '模型数', '结果数')
        self.sessions_tree = ttk.Treeview(left_frame, columns=columns, show='headings', height=15)
        
        # 设置列标题和宽度
        column_widths = {'会话ID': 150, '会话名称': 200, '状态': 80, '创建时间': 150, '模型数': 60, '结果数': 60}
        for col in columns:
            self.sessions_tree.heading(col, text=col)
            self.sessions_tree.column(col, width=column_widths.get(col, 100), anchor=tk.CENTER)
        
        # 滚动条
        sessions_scrollbar = ttk.Scrollbar(left_frame, orient=tk.VERTICAL, command=self.sessions_tree.yview)
        self.sessions_tree.configure(yscrollcommand=sessions_scrollbar.set)
        
        self.sessions_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        sessions_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
        # 绑定选择事件
        self.sessions_tree.bind('<<TreeviewSelect>>', self._on_session_select)
        
        # 右键菜单
        self._create_context_menu()
    
    def _create_session_details(self, parent):
        """创建会话详情面板"""
        # 右侧框架
        right_frame = ttk.LabelFrame(parent, text="会话详情")
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))
        
        # 详情文本框
        self.detail_text = tk.Text(right_frame, wrap=tk.WORD, font=('Consolas', 10))
        detail_scrollbar = ttk.Scrollbar(right_frame, orient=tk.VERTICAL, command=self.detail_text.yview)
        self.detail_text.configure(yscrollcommand=detail_scrollbar.set)
        
        self.detail_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        detail_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
        # 初始显示
        self._show_welcome_message()
    
    def _create_status_bar(self):
        """创建状态栏"""
        status_frame = ttk.Frame(self.frame)
        status_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Label(status_frame, text="状态:").pack(side=tk.LEFT)
        ttk.Label(status_frame, textvariable=self.status_var, foreground="blue").pack(side=tk.LEFT, padx=(5, 0))
        
        # 当前活动会话显示
        current_session = self.session_manager.get_current_session()
        if current_session:
            current_text = f"当前会话: {current_session.session_name}"
        else:
            current_text = "当前会话: 无"
        
        self.current_session_var = tk.StringVar(value=current_text)
        ttk.Label(status_frame, textvariable=self.current_session_var, foreground="green").pack(side=tk.RIGHT)
    
    def _create_context_menu(self):
        """创建右键菜单"""
        self.context_menu = tk.Menu(self.sessions_tree, tearoff=0)
        self.context_menu.add_command(label="激活会话", command=self._activate_session)
        self.context_menu.add_command(label="重命名会话", command=self._rename_session)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="导出报告", command=self._export_report)
        self.context_menu.add_command(label="复制会话", command=self._copy_session)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="删除会话", command=self._delete_session)
        
        # 绑定右键事件
        self.sessions_tree.bind("<Button-3>", self._show_context_menu)
    
    def _register_events(self):
        """注册事件监听"""
        self.event_manager.subscribe('session_created', self._on_session_created)
        self.event_manager.subscribe('session_saved', self._on_session_saved)
        self.event_manager.subscribe('session_activated', self._on_session_activated)
        self.event_manager.subscribe('session_deleted', self._on_session_deleted)
    
    def _refresh_sessions(self):
        """刷新会话列表"""
        try:
            self.status_var.set("正在刷新会话列表...")
            
            # 清空现有列表
            for item in self.sessions_tree.get_children():
                self.sessions_tree.delete(item)
            
            # 获取会话列表
            sessions = self.session_manager.list_sessions()
            
            # 添加到树形控件
            for session in sessions:
                session_id = session['session_id']
                session_name = session['session_name']
                status = session['status']
                created_time = datetime.fromisoformat(session['created_time']).strftime('%Y-%m-%d %H:%M')
                model_count = session.get('model_count', 0)
                result_count = session.get('result_count', 0)
                
                self.sessions_tree.insert('', 'end', values=(
                    session_id, session_name, status, created_time, model_count, result_count
                ))
            
            self.status_var.set(f"已加载 {len(sessions)} 个会话")
            
        except Exception as e:
            self.error_handler.handle_error(e, "刷新会话列表")
            self.status_var.set("刷新失败")
    
    def _on_session_select(self, event):
        """会话选择事件处理"""
        selection = self.sessions_tree.selection()
        if selection:
            item = self.sessions_tree.item(selection[0])
            self.selected_session_id = item['values'][0]
            self._show_session_details(self.selected_session_id)
    
    def _show_session_details(self, session_id: str):
        """显示会话详情"""
        try:
            session = self.session_manager.load_session(session_id)
            if session:
                details = self._format_session_details(session)
                self.detail_text.delete(1.0, tk.END)
                self.detail_text.insert(1.0, details)
            else:
                self.detail_text.delete(1.0, tk.END)
                self.detail_text.insert(1.0, "无法加载会话详情")
                
        except Exception as e:
            self.error_handler.handle_error(e, "显示会话详情")
    
    def _format_session_details(self, session: TrainingSession) -> str:
        """格式化会话详情"""
        details = f"""会话详细信息
{'='*50}

基本信息:
  会话ID: {session.session_id}
  会话名称: {session.session_name}
  描述: {session.description}
  状态: {session.status}
  创建时间: {session.created_time.strftime('%Y-%m-%d %H:%M:%S')}
  修改时间: {session.modified_time.strftime('%Y-%m-%d %H:%M:%S')}

统计信息:
  模型数量: {len(session.models)}
  结果数量: {len(session.results)}
  图表数量: {len(session.plots)}
  配置数量: {len(session.configs)}

模型列表:
"""
        
        for model_name, model_info in session.models.items():
            details += f"  • {model_name} ({model_info.get('type', 'unknown')})\n"
        
        if not session.models:
            details += "  (无模型)\n"
        
        details += "\n结果列表:\n"
        for result_name, result_info in session.results.items():
            details += f"  • {result_name} ({result_info.get('type', 'unknown')})\n"
        
        if not session.results:
            details += "  (无结果)\n"
        
        details += "\n图表列表:\n"
        for plot_name, plot_info in session.plots.items():
            details += f"  • {plot_name} ({plot_info.get('type', 'unknown')})\n"
        
        if not session.plots:
            details += "  (无图表)\n"
        
        return details
    
    def _show_welcome_message(self):
        """显示欢迎消息"""
        welcome_text = """欢迎使用训练会话管理器！

功能说明:
• 新建会话: 创建新的训练会话
• 激活会话: 设置选中会话为当前活动会话
• 保存会话: 保存当前会话的所有数据
• 加载会话: 加载选中的会话数据
• 导出报告: 生成会话的详细报告
• 删除会话: 永久删除选中的会话

使用提示:
1. 在左侧列表中选择会话查看详情
2. 右键点击会话可显示更多操作选项
3. 当前活动会话会在状态栏显示
4. 训练过程中会自动保存到当前会话

开始使用前，请先创建一个新会话或激活现有会话。
"""
        self.detail_text.delete(1.0, tk.END)
        self.detail_text.insert(1.0, welcome_text)
    
    def _new_session(self):
        """新建会话"""
        # 获取会话名称
        session_name = simpledialog.askstring("新建会话", "请输入会话名称:")
        if not session_name:
            return
        
        # 获取会话描述
        description = simpledialog.askstring("新建会话", "请输入会话描述 (可选):", initialvalue="")
        if description is None:
            description = ""
        
        try:
            # 创建会话
            session = self.session_manager.create_session(session_name, description)
            self.status_var.set(f"已创建会话: {session.session_name}")
            messagebox.showinfo("成功", f"会话 '{session.session_name}' 创建成功并已激活")
            
        except Exception as e:
            self.error_handler.handle_error(e, "创建会话")
            messagebox.showerror("错误", f"创建会话失败: {e}")
    
    def _activate_session(self):
        """激活会话"""
        if not self.selected_session_id:
            messagebox.showwarning("警告", "请先选择一个会话")
            return
        
        try:
            success = self.session_manager.activate_session(self.selected_session_id)
            if success:
                self.status_var.set(f"已激活会话: {self.selected_session_id}")
                messagebox.showinfo("成功", "会话激活成功")
            else:
                messagebox.showerror("错误", "会话激活失败")
                
        except Exception as e:
            self.error_handler.handle_error(e, "激活会话")
            messagebox.showerror("错误", f"激活会话失败: {e}")
    
    def _save_session(self):
        """保存会话"""
        current_session = self.session_manager.get_current_session()
        if not current_session:
            messagebox.showwarning("警告", "没有活动的会话可保存")
            return
        
        try:
            success = self.session_manager.save_session()
            if success:
                self.status_var.set("会话保存成功")
                messagebox.showinfo("成功", "当前会话已保存")
            else:
                messagebox.showerror("错误", "会话保存失败")
                
        except Exception as e:
            self.error_handler.handle_error(e, "保存会话")
            messagebox.showerror("错误", f"保存会话失败: {e}")
    
    def _load_session(self):
        """加载会话"""
        if not self.selected_session_id:
            messagebox.showwarning("警告", "请先选择一个会话")
            return
        
        try:
            session = self.session_manager.load_session(self.selected_session_id)
            if session:
                self.status_var.set(f"已加载会话: {session.session_name}")
                messagebox.showinfo("成功", f"会话 '{session.session_name}' 加载成功")
            else:
                messagebox.showerror("错误", "会话加载失败")
                
        except Exception as e:
            self.error_handler.handle_error(e, "加载会话")
            messagebox.showerror("错误", f"加载会话失败: {e}")
    
    def _delete_session(self):
        """删除会话"""
        if not self.selected_session_id:
            messagebox.showwarning("警告", "请先选择一个会话")
            return
        
        # 确认删除
        result = messagebox.askyesno("确认删除", 
                                   f"确定要删除会话 '{self.selected_session_id}' 吗？\n\n此操作不可撤销！")
        if not result:
            return
        
        try:
            success = self.session_manager.delete_session(self.selected_session_id)
            if success:
                self.status_var.set("会话删除成功")
                messagebox.showinfo("成功", "会话已删除")
                self.selected_session_id = None
                self._show_welcome_message()
            else:
                messagebox.showerror("错误", "会话删除失败")
                
        except Exception as e:
            self.error_handler.handle_error(e, "删除会话")
            messagebox.showerror("错误", f"删除会话失败: {e}")
    
    def _export_report(self):
        """导出报告"""
        if not self.selected_session_id:
            messagebox.showwarning("警告", "请先选择一个会话")
            return
        
        # 这里可以实现报告导出功能
        messagebox.showinfo("提示", "报告导出功能待实现")
    
    def _rename_session(self):
        """重命名会话"""
        if not self.selected_session_id:
            messagebox.showwarning("警告", "请先选择一个会话")
            return
        
        # 获取新名称
        new_name = simpledialog.askstring("重命名会话", "请输入新的会话名称:")
        if not new_name:
            return
        
        try:
            session = self.session_manager.load_session(self.selected_session_id)
            if session:
                session.session_name = new_name
                session.modified_time = datetime.now()
                self.session_manager.save_session(session)
                self.status_var.set("会话重命名成功")
                messagebox.showinfo("成功", "会话重命名成功")
            else:
                messagebox.showerror("错误", "无法加载会话")
                
        except Exception as e:
            self.error_handler.handle_error(e, "重命名会话")
            messagebox.showerror("错误", f"重命名会话失败: {e}")
    
    def _copy_session(self):
        """复制会话"""
        messagebox.showinfo("提示", "会话复制功能待实现")
    
    def _show_statistics(self):
        """显示统计信息"""
        try:
            sessions = self.session_manager.list_sessions()
            
            total_sessions = len(sessions)
            total_models = sum(s.get('model_count', 0) for s in sessions)
            total_results = sum(s.get('result_count', 0) for s in sessions)
            
            # 按状态统计
            status_counts = {}
            for session in sessions:
                status = session.get('status', 'unknown')
                status_counts[status] = status_counts.get(status, 0) + 1
            
            stats_text = f"""会话统计信息
{'='*30}

总体统计:
  总会话数: {total_sessions}
  总模型数: {total_models}
  总结果数: {total_results}

状态分布:
"""
            
            for status, count in status_counts.items():
                stats_text += f"  {status}: {count}\n"
            
            # 显示统计窗口
            stats_window = tk.Toplevel(self.parent)
            stats_window.title("统计信息")
            stats_window.geometry("400x300")
            
            text_widget = tk.Text(stats_window, wrap=tk.WORD, font=('Consolas', 10))
            text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            text_widget.insert(1.0, stats_text)
            text_widget.config(state=tk.DISABLED)
            
        except Exception as e:
            self.error_handler.handle_error(e, "显示统计信息")
            messagebox.showerror("错误", f"获取统计信息失败: {e}")
    
    def _show_context_menu(self, event):
        """显示右键菜单"""
        try:
            self.context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.context_menu.grab_release()
    
    def _on_session_created(self, event_data):
        """会话创建事件处理"""
        self._refresh_sessions()
        self._update_current_session_display()
    
    def _on_session_saved(self, event_data):
        """会话保存事件处理"""
        self._refresh_sessions()
    
    def _on_session_activated(self, event_data):
        """会话激活事件处理"""
        self._update_current_session_display()
    
    def _on_session_deleted(self, event_data):
        """会话删除事件处理"""
        self._refresh_sessions()
        self._update_current_session_display()
    
    def _update_current_session_display(self):
        """更新当前会话显示"""
        current_session = self.session_manager.get_current_session()
        if current_session:
            current_text = f"当前会话: {current_session.session_name}"
        else:
            current_text = "当前会话: 无"
        
        self.current_session_var.set(current_text)
    
    def get_frame(self):
        """获取主框架"""
        return self.frame
