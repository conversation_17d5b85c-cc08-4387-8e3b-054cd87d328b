#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型管理器
负责机器学习模型的创建、训练和管理
"""

import logging
import time
from typing import Dict, Any, Optional, List, Tuple
import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score
from sklearn.metrics import confusion_matrix, classification_report

# 导入各种模型
from sklearn.tree import DecisionTreeClassifier
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.neighbors import KNeighborsClassifier
from sklearn.naive_bayes import GaussianNB
from sklearn.neural_network import MLPClassifier

try:
    import xgboost as xgb
    HAS_XGBOOST = True
except ImportError:
    HAS_XGBOOST = False

try:
    import lightgbm as lgb
    HAS_LIGHTGBM = True
except ImportError:
    HAS_LIGHTGBM = False

try:
    import catboost as cb
    HAS_CATBOOST = True
except ImportError:
    HAS_CATBOOST = False

from .config_manager import get_config

# 导入算法模块
try:
    from ..algorithms import (
        MODEL_TRAINERS, MODEL_NAMES, MODEL_DISPLAY_NAMES,
        train_decision_tree, train_random_forest, train_xgboost,
        train_lightgbm, train_catboost, train_logistic,
        train_svm, train_knn, train_naive_bayes, train_neural_net
    )
    HAS_ALGORITHMS = True
except ImportError:
    # 如果算法模块不可用，使用空的配置
    MODEL_TRAINERS = {}
    MODEL_NAMES = []
    MODEL_DISPLAY_NAMES = {}
    HAS_ALGORITHMS = False


class ModelManager:
    """模型管理器"""
    
    def __init__(self):
        """初始化模型管理器"""
        self.logger = logging.getLogger(__name__)
        self.config = get_config()
        self.models = {}
        self.trained_models = {}
        self._init_models()
    
    def _init_models(self):
        """初始化可用模型"""
        model_config = self.config.get_model_config()
        random_state = model_config.get('random_state', 42)

        # 如果有算法模块，使用算法模块的模型
        if HAS_ALGORITHMS and MODEL_TRAINERS:
            self.models = {}
            for model_name, trainer in MODEL_TRAINERS.items():
                # 使用算法模块的默认参数创建模型
                params = trainer.default_params.copy()
                self.models[model_name] = trainer.model_class(**params)

            # 模型训练函数映射
            self.training_functions = {
                'DecisionTree': train_decision_tree,
                'RandomForest': train_random_forest,
                'XGBoost': train_xgboost,
                'LightGBM': train_lightgbm,
                'CatBoost': train_catboost,
                'Logistic': train_logistic,
                'SVM': train_svm,
                'KNN': train_knn,
                'NaiveBayes': train_naive_bayes,
                'NeuralNet': train_neural_net
            }
        else:
            # 回退到原有的模型初始化方式
            self.models = {
                'DecisionTree': DecisionTreeClassifier(random_state=random_state),
                'RandomForest': RandomForestClassifier(random_state=random_state, n_estimators=100),
                'Logistic': LogisticRegression(random_state=random_state, max_iter=1000),
                'SVM': SVC(random_state=random_state, probability=True),
                'KNN': KNeighborsClassifier(n_neighbors=5),
                'NaiveBayes': GaussianNB(),
                'NeuralNet': MLPClassifier(random_state=random_state, max_iter=500)
            }

            # 添加可选的第三方模型
            if HAS_XGBOOST:
                self.models['XGBoost'] = xgb.XGBClassifier(random_state=random_state)

            if HAS_LIGHTGBM:
                self.models['LightGBM'] = lgb.LGBMClassifier(random_state=random_state)

            if HAS_CATBOOST:
                self.models['CatBoost'] = cb.CatBoostClassifier(random_state=random_state, verbose=False)

            self.training_functions = {}
    
    def get_available_models(self) -> List[str]:
        """获取可用模型列表"""
        return list(self.models.keys())

    def get_model_display_name(self, model_name: str) -> str:
        """
        获取模型显示名称

        Args:
            model_name: 模型名称

        Returns:
            显示名称
        """
        if HAS_ALGORITHMS and MODEL_DISPLAY_NAMES:
            return MODEL_DISPLAY_NAMES.get(model_name, model_name)
        else:
            # 默认的显示名称映射
            display_names = {
                'DecisionTree': '决策树',
                'RandomForest': '随机森林',
                'XGBoost': 'XGBoost',
                'LightGBM': 'LightGBM',
                'CatBoost': 'CatBoost',
                'Logistic': '逻辑回归',
                'SVM': '支持向量机',
                'KNN': 'K近邻',
                'NaiveBayes': '朴素贝叶斯',
                'NeuralNet': '神经网络'
            }
            return display_names.get(model_name, model_name)
    
    def train_model(self, model_name: str, X_train: pd.DataFrame, y_train: pd.Series,
                   X_test: pd.DataFrame, y_test: pd.Series, params: Optional[Dict] = None) -> Dict[str, Any]:
        """
        训练单个模型

        Args:
            model_name: 模型名称
            X_train: 训练特征
            y_train: 训练标签
            X_test: 测试特征
            y_test: 测试标签
            params: 模型参数

        Returns:
            训练结果字典
        """
        if model_name not in self.models:
            raise ValueError(f"未知模型: {model_name}")

        try:
            # 如果有算法模块的训练函数，使用算法模块
            if HAS_ALGORITHMS and model_name in self.training_functions:
                training_func = self.training_functions[model_name]
                result = training_func(X_train, y_train, X_test, y_test, params)

                # 保存训练好的模型
                self.trained_models[model_name] = result['model']

                # 转换结果格式以保持兼容性
                converted_result = {
                    'model_name': model_name,
                    'accuracy': result['metrics']['accuracy'],
                    'precision': result['metrics'].get('precision', 0.0),
                    'recall': result['metrics'].get('recall', 0.0),
                    'f1_score': result['metrics'].get('f1', 0.0),
                    'auc': result['metrics'].get('auc', 0.0),
                    'training_time': result['training_time'],
                    'confusion_matrix': [],  # 需要从原始结果计算
                    'classification_report': '',  # 需要从原始结果计算
                    'model': result['model'],
                    'y_pred': result.get('y_pred'),
                    'y_pred_proba': result.get('y_pred_proba'),
                    'original_result': result  # 保存原始结果
                }

                self.logger.info(f"模型 {model_name} 训练完成（使用算法模块），准确率: {converted_result['accuracy']:.4f}")
                return converted_result

            else:
                # 使用原有的训练方式
                # 记录开始时间
                start_time = time.time()

                # 获取模型实例
                model = self.models[model_name]

                # 训练模型
                model.fit(X_train, y_train)

                # 预测
                y_pred = model.predict(X_test)
                y_pred_proba = None

                # 获取预测概率（如果支持）
                if hasattr(model, 'predict_proba'):
                    try:
                        y_pred_proba = model.predict_proba(X_test)[:, 1]
                    except:
                        pass

                # 计算指标
                accuracy = accuracy_score(y_test, y_pred)
                precision = precision_score(y_test, y_pred, average='weighted', zero_division=0)
                recall = recall_score(y_test, y_pred, average='weighted', zero_division=0)
                f1 = f1_score(y_test, y_pred, average='weighted', zero_division=0)

                # 计算AUC（如果是二分类且有概率预测）
                auc = 0.0
                if y_pred_proba is not None and len(np.unique(y_test)) == 2:
                    try:
                        auc = roc_auc_score(y_test, y_pred_proba)
                    except:
                        pass

                # 计算训练时间
                training_time = time.time() - start_time

                # 混淆矩阵
                cm = confusion_matrix(y_test, y_pred)

                # 分类报告
                cr = classification_report(y_test, y_pred, zero_division=0)

                # 保存训练好的模型
                self.trained_models[model_name] = model

                result = {
                    'model_name': model_name,
                    'accuracy': accuracy,
                    'precision': precision,
                    'recall': recall,
                    'f1_score': f1,
                    'auc': auc,
                    'training_time': training_time,
                    'confusion_matrix': cm.tolist(),
                    'classification_report': cr,
                    'model': model,
                    'y_pred': y_pred,
                    'y_pred_proba': y_pred_proba
                }

                self.logger.info(f"模型 {model_name} 训练完成，准确率: {accuracy:.4f}")
                return result

        except Exception as e:
            self.logger.error(f"训练模型 {model_name} 时出错: {e}")
            raise
    
    def train_multiple_models(self, model_names: List[str], X_train: pd.DataFrame, 
                            y_train: pd.Series, X_test: pd.DataFrame, 
                            y_test: pd.Series) -> Dict[str, Dict[str, Any]]:
        """
        训练多个模型
        
        Args:
            model_names: 模型名称列表
            X_train: 训练特征
            y_train: 训练标签
            X_test: 测试特征
            y_test: 测试标签
            
        Returns:
            训练结果字典
        """
        results = {}
        
        for model_name in model_names:
            try:
                result = self.train_model(model_name, X_train, y_train, X_test, y_test)
                results[model_name] = result
            except Exception as e:
                self.logger.error(f"训练模型 {model_name} 失败: {e}")
                results[model_name] = {
                    'error': str(e),
                    'model_name': model_name
                }
        
        return results
    
    def get_trained_model(self, model_name: str):
        """获取已训练的模型"""
        return self.trained_models.get(model_name)
    
    def clear_trained_models(self):
        """清空已训练的模型"""
        self.trained_models.clear()
        self.logger.info("已清空所有训练的模型")
    
    def cross_validate_model(self, model_name: str, X: pd.DataFrame, 
                           y: pd.Series, cv: int = 5) -> Dict[str, Any]:
        """
        交叉验证模型
        
        Args:
            model_name: 模型名称
            X: 特征数据
            y: 标签数据
            cv: 交叉验证折数
            
        Returns:
            交叉验证结果
        """
        if model_name not in self.models:
            raise ValueError(f"未知模型: {model_name}")
        
        try:
            model = self.models[model_name]
            scores = cross_val_score(model, X, y, cv=cv, scoring='accuracy')
            
            result = {
                'model_name': model_name,
                'cv_scores': scores.tolist(),
                'mean_score': scores.mean(),
                'std_score': scores.std(),
                'cv_folds': cv
            }
            
            self.logger.info(f"模型 {model_name} 交叉验证完成，平均准确率: {scores.mean():.4f}")
            return result
            
        except Exception as e:
            self.logger.error(f"交叉验证模型 {model_name} 时出错: {e}")
            raise
