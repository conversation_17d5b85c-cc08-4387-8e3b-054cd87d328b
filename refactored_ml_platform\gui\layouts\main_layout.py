#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主界面布局模块
提供完整的主界面布局，包括菜单栏、工具栏、导航面板、工作区域等
"""

import tkinter as tk
from tkinter import ttk, messagebox


from ..core.base_gui import BaseGUI
from ..core.component_factory import get_component_factory
from ..core.event_manager import EventTypes
from ..modules.data_management.data_management_module import DataManagementModule
from ..modules.model_training.model_training_manager import ModelTrainingManager
from ..modules.visualization.visualization_manager import VisualizationManager


class MainWindow(BaseGUI):
    """
    主窗口类
    提供完整的应用程序主界面
    """
    
    def __init__(self, parent: tk.Tk):
        """初始化主窗口"""
        self.current_tab = "data_management"
        super().__init__(parent)
        
        # 绑定事件
        self._bind_events()
    
    def _setup_ui(self):
        """设置主界面UI"""
        factory = get_component_factory()
        
        # 主框架
        if self.parent:
            self.main_frame = factory.create_frame(self.parent)
            self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建菜单栏
        self._create_menu_bar()
        
        # 创建工具栏
        self._create_toolbar()
        
        # 创建主要内容区域
        self._create_main_content()
        
        # 创建状态栏
        self._create_status_bar()
    
    def _create_menu_bar(self):
        """创建菜单栏"""
        if self.parent:
            menubar = tk.Menu(self.parent)
            # 类型检查：使用类型转换解决tk.Tk的config属性访问
            if hasattr(self.parent, 'config'):
                # 类型忽略：确保运行时parent是tk.Tk类型
                self.parent.config(menu=menubar)  # type: ignore
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="打开数据文件...", command=self._open_data_file)
        file_menu.add_command(label="保存项目", command=self._save_project)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self._on_exit)
        
        # 工具菜单
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="工具", menu=tools_menu)
        tools_menu.add_command(label="数据探索", command=lambda: self._switch_tab("data_management"))
        tools_menu.add_command(label="模型训练", command=lambda: self._switch_tab("model_training"))
        tools_menu.add_command(label="结果可视化", command=lambda: self._switch_tab("visualization"))
        tools_menu.add_command(label="集成学习", command=lambda: self._switch_tab("ensemble"))
        
        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用指南", command=self._show_help)
        help_menu.add_command(label="关于", command=self._show_about)
        
        self.register_component('menubar', menubar)
    
    def _create_toolbar(self):
        """创建工具栏"""
        if not self.main_frame:
            return
            
        factory = get_component_factory()
        toolbar = factory.create_frame(self.main_frame, relief=tk.RAISED, bd=1)
        toolbar.pack(fill=tk.X, padx=2, pady=2)
        
        # 快捷按钮
        buttons = [
            ("📁 打开数据", self._open_data_file, "打开数据文件"),
            ("💾 保存项目", self._save_project, "保存当前项目"),
            ("🔄 刷新", self._refresh_data, "刷新数据"),
            ("⚙️ 设置", self._show_settings, "打开设置"),
        ]
        
        for text, command, tooltip in buttons:
            btn = factory.create_button(toolbar, text=text, command=command, style='default')
            btn.pack(side=tk.LEFT, padx=2, pady=2)
            
            # 添加工具提示
            from ..core.utils import GUIUtils
            GUIUtils.create_tooltip(btn, tooltip)
        
        # 分隔符
        separator = ttk.Separator(toolbar, orient=tk.VERTICAL)
        separator.pack(side=tk.LEFT, fill=tk.Y, padx=5, pady=2)
        
        # 状态指示器
        from ..components.progress_widgets import StatusIndicator
        self.status_indicator = StatusIndicator(toolbar)
        
        self.register_component('toolbar', toolbar)
    
    def _create_main_content(self):
        """创建主要内容区域"""
        if not self.main_frame:
            return
            
        factory = get_component_factory()
        
        # 主内容区域
        content_frame = factory.create_frame(self.main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建左右分割面板
        paned_window = ttk.PanedWindow(content_frame, orient=tk.HORIZONTAL)
        paned_window.pack(fill=tk.BOTH, expand=True)
        
        # 左侧导航面板
        self._create_navigation_panel(paned_window)
        
        # 右侧工作区域
        self._create_work_area(paned_window)
        
        self.register_component('content_frame', content_frame)
        self.register_component('paned_window', paned_window)
    
    def _create_navigation_panel(self, parent):
        """创建左侧导航面板"""
        if not parent:
            return
            
        factory = get_component_factory()
        
        # 导航框架
        nav_frame = factory.create_frame(parent, style='card')
        nav_frame.pack(fill=tk.BOTH, expand=True)
        
        # 导航标题
        nav_title = factory.create_label(nav_frame, text="功能导航", style='title')
        nav_title.pack(pady=10)
        
        # 导航树
        self.nav_tree = factory.create_treeview(nav_frame, show='tree')
        self.nav_tree.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))
        
        # 添加导航项
        nav_items = [
            ("data_management", "📊 数据管理", [
                ("data_loader", "📁 数据加载"),
                ("data_preview", "👁️ 数据预览"),
                ("data_validation", "✅ 数据验证"),
                ("data_preprocessing", "⚙️ 数据预处理")
            ]),
            ("model_training", "🤖 模型训练", [
                ("model_selection", "🎯 模型选择"),
                ("training_config", "⚙️ 训练配置"),
                ("hyperparameter_tuning", "🔧 超参数调优"),
                ("training_monitor", "📈 训练监控")
            ]),
            ("visualization", "📈 结果可视化", [
                ("chart_manager", "📊 图表管理"),
                ("model_comparison", "🔍 模型比较"),
                ("shap_analysis", "🧠 SHAP分析"),
                ("report_generator", "📄 报告生成")
            ]),
            ("ensemble", "🔗 集成学习", [
                ("model_selector", "🎯 模型选择"),
                ("ensemble_config", "⚙️ 集成配置"),
                ("performance_evaluation", "📊 性能评估")
            ]),
            ("session", "💾 会话管理", [
                ("session_create", "➕ 创建会话"),
                ("session_load", "📂 加载会话"),
                ("session_save", "💾 保存会话")
            ])
        ]
        
        for parent_id, parent_text, children in nav_items:
            parent_node = self.nav_tree.insert('', 'end', iid=parent_id, text=parent_text, open=True)
            for child_id, child_text in children:
                self.nav_tree.insert(parent_node, 'end', iid=child_id, text=child_text)
        
        # 绑定选择事件
        self.nav_tree.bind('<<TreeviewSelect>>', self._on_nav_select)
        
        # 将导航面板添加到分割窗口
        parent.add(nav_frame, weight=1)
        
        self.register_component('nav_frame', nav_frame)
        self.register_component('nav_tree', self.nav_tree)
    
    def _create_work_area(self, parent):
        """创建右侧工作区域"""
        factory = get_component_factory()
        
        # 工作区框架
        work_frame = factory.create_frame(parent)
        work_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建标签页容器
        self.notebook = factory.create_notebook(work_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建各个功能模块标签页
        self._create_module_tabs()
        
        # 将工作区添加到分割窗口
        parent.add(work_frame, weight=3)
        
        self.register_component('work_frame', work_frame)
        self.register_component('notebook', self.notebook)
    
    def _create_module_tabs(self):
        """创建功能模块标签页"""
        # 数据管理模块
        self.data_management_module = DataManagementModule(self.notebook)
        if self.data_management_module.main_frame:
            self.notebook.add(self.data_management_module.main_frame, text="数据管理")
        
        # 模型训练管理模块
        self.model_training_manager = ModelTrainingManager(self.notebook)
        if self.model_training_manager.main_frame:
            self.notebook.add(self.model_training_manager.main_frame, text="模型训练")
        
        # 可视化管理模块
        self.visualization_manager = VisualizationManager(self.notebook)
        if self.visualization_manager.main_frame:
            self.notebook.add(self.visualization_manager.main_frame, text="结果可视化")
        
        # 其他模块的占位符标签页
        self._create_placeholder_tabs()
    
    def _create_placeholder_tabs(self):
        """创建其他模块的占位符标签页"""
        factory = get_component_factory()
        
        placeholder_modules = [
            ("集成学习", "集成学习功能正在开发中..."),
            ("会话管理", "会话管理功能正在开发中...")
        ]
        
        for tab_text, placeholder_text in placeholder_modules:
            placeholder_frame = factory.create_frame(self.notebook)
            
            # 占位符内容
            placeholder_label = factory.create_label(
                placeholder_frame, 
                text=placeholder_text,
                style='title'
            )
            placeholder_label.pack(expand=True)
            
            self.notebook.add(placeholder_frame, text=tab_text)
    
    def _create_status_bar(self):
        """创建状态栏"""
        if not self.main_frame:
            return
            
        factory = get_component_factory()
        status_frame = factory.create_frame(self.main_frame, relief=tk.SUNKEN, bd=1)
        if status_frame:
            status_frame.pack(fill=tk.X, side=tk.BOTTOM)
        
        # 状态文本
        self.status_var = tk.StringVar(value="就绪")
        status_label = factory.create_label(status_frame, textvariable=self.status_var, style='small')
        status_label.pack(side=tk.LEFT, padx=5, pady=2)
        
        # 进度条
        from ..components.progress_widgets import ProgressWidget
        self.progress_widget = ProgressWidget(status_frame, show_percentage=False, show_status=False)
        if self.progress_widget.main_frame:
            self.progress_widget.main_frame.pack(side=tk.RIGHT, padx=5, pady=2)
        
        # 时间显示
        self.time_var = tk.StringVar()
        time_label = factory.create_label(status_frame, textvariable=self.time_var, style='small')
        time_label.pack(side=tk.RIGHT, padx=5, pady=2)
        
        # 更新时间
        self._update_time()
        
        self.register_component('status_frame', status_frame)
        self.register_variable('status', self.status_var)
        self.register_variable('time', self.time_var)
    
    def _bind_events(self):
        """绑定事件"""
        # 订阅数据相关事件
        self.subscribe_event(EventTypes.DATA_LOADED, self._on_data_loaded)
        self.subscribe_event(EventTypes.DATA_PREPROCESSED, self._on_data_preprocessed)
        
        # 订阅配置更新事件
        self.subscribe_event(EventTypes.CONFIG_UPDATED, self._on_config_updated)
    
    def _on_nav_select(self, event):
        """导航树选择事件处理"""
        selection = self.nav_tree.selection()
        if selection:
            item_id = selection[0]
            self._handle_navigation(item_id)
    
    def _handle_navigation(self, item_id: str):
        """处理导航选择"""
        # 根据选择的项目切换到对应的功能
        if item_id.startswith("data_"):
            self.notebook.select(0)  # 数据管理标签页
        elif item_id.startswith("model_"):
            self.notebook.select(1)  # 模型训练标签页
        elif item_id.startswith("chart_") or item_id.startswith("shap_"):
            self.notebook.select(2)  # 可视化标签页
        elif item_id.startswith("ensemble_"):
            self.notebook.select(3)  # 集成学习标签页
        elif item_id.startswith("session_"):
            self.notebook.select(4)  # 会话管理标签页
        
        self.status_var.set(f"已切换到: {self.nav_tree.item(item_id, 'text')}")
    
    def _switch_tab(self, tab_name: str):
        """切换标签页"""
        tab_mapping = {
            "data_management": 0,
            "model_training": 1,
            "visualization": 2,
            "ensemble": 3,
            "session": 4
        }
        
        if tab_name in tab_mapping:
            self.notebook.select(tab_mapping[tab_name])
            self.current_tab = tab_name
    
    def _update_time(self):
        """更新时间显示"""
        import datetime
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_var.set(current_time)
        
        # 每秒更新一次
        if self.parent:
            self.parent.after(1000, self._update_time)
    
    # 菜单和工具栏事件处理
    def _open_data_file(self):
        """打开数据文件"""
        from ..core.utils import GUIUtils
        
        file_path = GUIUtils.browse_file(
            title="选择数据文件",
            filetypes=[("CSV文件", "*.csv"), ("所有文件", "*.*")]
        )
        
        if file_path:
            # 切换到数据管理标签页
            self._switch_tab("data_management")
            
            # 设置文件路径到数据管理模块
            self.data_management_module.file_selector.set_file_path(file_path)
            
            self.status_var.set(f"已选择文件: {file_path}")
    
    def _save_project(self):
        """保存项目"""
        self.show_info("保存项目", "项目保存功能正在开发中...")
    
    def _refresh_data(self):
        """刷新数据"""
        self.status_var.set("正在刷新数据...")
        # 这里可以添加刷新逻辑
        self.status_var.set("数据刷新完成")
    
    def _show_settings(self):
        """显示设置"""
        self.show_info("设置", "设置功能正在开发中...")
    
    def _show_help(self):
        """显示帮助"""
        help_text = """
重构版多模型集成机器学习平台使用指南

1. 数据管理：
   - 使用"数据加载"功能导入CSV文件
   - 在"数据预览"中查看数据概况
   - 通过"数据验证"检查数据质量
   - 使用"数据预处理"清洗和准备数据

2. 模型训练：
   - 选择合适的机器学习模型
   - 配置训练参数
   - 启动训练过程

3. 结果可视化：
   - 查看模型性能图表
   - 进行模型比较分析
   - 生成分析报告

4. 集成学习：
   - 选择最优模型组合
   - 配置集成策略
   - 评估集成效果

5. 会话管理：
   - 保存和恢复训练会话
   - 管理历史实验记录
        """
        
        # 创建帮助窗口
        help_window = tk.Toplevel(self.parent)
        help_window.title("使用指南")
        help_window.geometry("600x400")
        
        text_widget = tk.Text(help_window, wrap=tk.WORD, padx=10, pady=10)
        text_widget.pack(fill=tk.BOTH, expand=True)
        text_widget.insert(tk.END, help_text)
        text_widget.config(state=tk.DISABLED)
    
    def _show_about(self):
        """显示关于信息"""
        about_text = """
重构版多模型集成机器学习平台 v2.0

基于模块化架构设计的新版本，具有以下特点：
• 模块化设计，易于维护和扩展
• 事件驱动架构，模块间解耦
• 统一的配置管理系统
• 可复用的GUI组件库
• 完整的日志记录系统

技术栈：
• Python 3.7+
• tkinter GUI框架
• pandas 数据处理
• scikit-learn 机器学习
• matplotlib/seaborn 可视化

开发团队：AI助手 Cascade
版本：2.0 (重构版)
更新日期：2025-08-07
        """
        
        messagebox.showinfo("关于", about_text)
    
    def _on_exit(self):
        """退出应用程序"""
        if self.ask_yes_no("确认退出", "确定要退出应用程序吗?"):
            if self.parent:
                self.parent.quit()
    
    # 事件处理方法
    def _on_data_loaded(self, data):
        """数据加载完成事件处理"""
        self.status_var.set("数据加载完成")
        self.status_indicator.set_status("success", "数据已加载")
    
    def _on_data_preprocessed(self, data):
        """数据预处理完成事件处理"""
        self.status_var.set("数据预处理完成")
        self.status_indicator.set_status("success", "数据预处理完成")
    
    def _on_config_updated(self, data):
        """配置更新事件处理"""
        self.status_var.set("配置已更新")
    
    def get_current_module(self):
        """获取当前活动的模块"""
        current_tab_index = self.notebook.index(self.notebook.select())
        
        if current_tab_index == 0:
            return self.data_management_module
        # 其他模块待实现
        return None
