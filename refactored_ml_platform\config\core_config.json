{"models": {"default_test_size": 0.2, "random_state": 42, "cv_folds": 5, "scoring": "accuracy", "n_jobs": -1}, "data": {"max_file_size_mb": 500, "supported_formats": ["csv", "xlsx", "json"], "encoding": "utf-8", "missing_threshold": 0.5}, "training": {"timeout_seconds": 3600, "early_stopping": true, "save_models": true, "model_dir": "models"}, "visualization": {"figure_size": [10, 6], "dpi": 100, "style": "seaborn", "color_palette": "viridis"}, "logging": {"level": "INFO", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "max_file_size_mb": 10, "backup_count": 5}}