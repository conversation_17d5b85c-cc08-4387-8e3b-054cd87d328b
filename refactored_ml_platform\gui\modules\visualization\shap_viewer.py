#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SHAP可视化查看器模块
提供SHAP分析结果的交互式查看功能
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
import pandas as pd
import numpy as np
from pathlib import Path
import threading
import time

from ....utils.shap_analyzer import ShapAnalyzer
from ....core.event_manager import get_event_manager
from ....utils.error_handler import get_error_handler


class SHAPViewerModule:
    """SHAP查看器模块"""
    
    def __init__(self, parent):
        """初始化SHAP查看器模块"""
        self.parent = parent
        self.event_manager = get_event_manager()
        self.error_handler = get_error_handler()
        
        # SHAP分析器
        self.shap_analyzer = ShapAnalyzer()
        
        # 数据存储
        self.current_model = None
        self.current_X_test = None
        self.current_model_name = ""
        self.current_plot_type = "摘要图"
        self.current_sample_idx = 0
        self.current_feature_idx = 0
        
        # GUI组件
        self.frame = None
        self.canvas = None
        self.toolbar = None
        self.fig = None
        self.ax = None
        
        # 控制变量
        self.plot_type_var = tk.StringVar(value="摘要图")
        self.sample_idx_var = tk.StringVar(value="0")
        self.feature_idx_var = tk.StringVar(value="0")
        self.status_var = tk.StringVar(value="就绪")
        
        # 创建界面
        self._create_interface()
        
        # 注册事件监听
        self._register_events()
    
    def _create_interface(self):
        """创建界面"""
        # 主框架
        self.frame = ttk.Frame(self.parent)
        self.frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 控制面板
        self._create_control_panel()
        
        # 图表显示区域
        self._create_chart_area()
        
        # 状态栏
        self._create_status_bar()
    
    def _create_control_panel(self):
        """创建控制面板"""
        control_frame = ttk.LabelFrame(self.frame, text="SHAP分析控制")
        control_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 第一行：图表类型选择
        row1 = ttk.Frame(control_frame)
        row1.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(row1, text="图表类型:").pack(side=tk.LEFT, padx=(0, 5))
        
        plot_types = ["摘要图", "依赖图", "瀑布图", "力图"]
        self.plot_type_combo = ttk.Combobox(
            row1, textvariable=self.plot_type_var,
            values=plot_types, state="readonly", width=12
        )
        self.plot_type_combo.pack(side=tk.LEFT, padx=(0, 10))
        self.plot_type_combo.bind('<<ComboboxSelected>>', self._on_plot_type_change)
        
        # 样本索引选择（用于瀑布图和力图）
        ttk.Label(row1, text="样本索引:").pack(side=tk.LEFT, padx=(20, 5))
        self.sample_spinbox = tk.Spinbox(
            row1, textvariable=self.sample_idx_var,
            from_=0, to=0, width=8, state="readonly"
        )
        self.sample_spinbox.pack(side=tk.LEFT, padx=(0, 10))
        self.sample_spinbox.bind('<Return>', self._on_sample_change)
        
        # 特征索引选择（用于依赖图）
        ttk.Label(row1, text="特征索引:").pack(side=tk.LEFT, padx=(20, 5))
        self.feature_spinbox = tk.Spinbox(
            row1, textvariable=self.feature_idx_var,
            from_=0, to=0, width=8, state="readonly"
        )
        self.feature_spinbox.pack(side=tk.LEFT, padx=(0, 10))
        self.feature_spinbox.bind('<Return>', self._on_feature_change)
        
        # 第二行：操作按钮
        row2 = ttk.Frame(control_frame)
        row2.pack(fill=tk.X, padx=5, pady=(0, 5))
        
        ttk.Button(row2, text="🔄 刷新", command=self._refresh_plot).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(row2, text="💾 保存图表", command=self._save_plot).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(row2, text="📊 重新分析", command=self._reanalyze).pack(side=tk.LEFT, padx=(0, 5))
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            row2, variable=self.progress_var, 
            mode='determinate', length=200
        )
        self.progress_bar.pack(side=tk.RIGHT, padx=(10, 0))
    
    def _create_chart_area(self):
        """创建图表显示区域"""
        chart_frame = ttk.LabelFrame(self.frame, text="SHAP图表显示")
        chart_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建matplotlib图表
        self.fig, self.ax = plt.subplots(figsize=(10, 6))
        self.fig.patch.set_facecolor('white')
        
        # 创建画布
        self.canvas = FigureCanvasTkAgg(self.fig, chart_frame)
        self.canvas.draw()
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 创建工具栏
        toolbar_frame = ttk.Frame(chart_frame)
        toolbar_frame.pack(fill=tk.X)
        self.toolbar = NavigationToolbar2Tk(self.canvas, toolbar_frame)
        self.toolbar.update()
        
        # 初始化空白图表
        self._show_empty_plot()
    
    def _create_status_bar(self):
        """创建状态栏"""
        status_frame = ttk.Frame(self.frame)
        status_frame.pack(fill=tk.X, padx=5, pady=(0, 5))
        
        ttk.Label(status_frame, text="状态:").pack(side=tk.LEFT)
        ttk.Label(status_frame, textvariable=self.status_var, foreground="blue").pack(side=tk.LEFT, padx=(5, 0))
    
    def _register_events(self):
        """注册事件监听"""
        self.event_manager.subscribe('model_trained', self._on_model_trained)
        self.event_manager.subscribe('data_loaded', self._on_data_loaded)
    
    def _show_empty_plot(self):
        """显示空白图表"""
        self.ax.clear()
        self.ax.text(0.5, 0.5, '请先训练模型并加载数据\n然后点击"重新分析"开始SHAP分析', 
                    ha='center', va='center', transform=self.ax.transAxes,
                    fontsize=14, color='gray')
        self.ax.set_xticks([])
        self.ax.set_yticks([])
        self.canvas.draw()
    
    def _on_model_trained(self, event_data):
        """模型训练完成事件处理"""
        self.current_model = event_data.get('model')
        self.current_model_name = event_data.get('model_name', 'Unknown')
        self.status_var.set(f"模型 {self.current_model_name} 已训练完成，可进行SHAP分析")
    
    def _on_data_loaded(self, event_data):
        """数据加载完成事件处理"""
        self.current_X_test = event_data.get('X_test')
        if self.current_X_test is not None:
            # 更新样本和特征索引范围
            max_samples = len(self.current_X_test) - 1
            max_features = len(self.current_X_test.columns) - 1
            
            self.sample_spinbox.config(to=max_samples)
            self.feature_spinbox.config(to=max_features)
            
            self.status_var.set(f"数据已加载，样本数: {len(self.current_X_test)}, 特征数: {len(self.current_X_test.columns)}")
    
    def _on_plot_type_change(self, event=None):
        """图表类型改变事件处理"""
        self.current_plot_type = self.plot_type_var.get()
        self._refresh_plot()
    
    def _on_sample_change(self, event=None):
        """样本索引改变事件处理"""
        try:
            self.current_sample_idx = int(self.sample_idx_var.get())
            if self.current_plot_type in ["瀑布图", "力图"]:
                self._refresh_plot()
        except ValueError:
            pass
    
    def _on_feature_change(self, event=None):
        """特征索引改变事件处理"""
        try:
            self.current_feature_idx = int(self.feature_idx_var.get())
            if self.current_plot_type == "依赖图":
                self._refresh_plot()
        except ValueError:
            pass
    
    def _reanalyze(self):
        """重新进行SHAP分析"""
        if self.current_model is None or self.current_X_test is None:
            messagebox.showwarning("警告", "请先训练模型并加载数据")
            return
        
        # 在后台线程中进行SHAP分析
        threading.Thread(target=self._perform_shap_analysis, daemon=True).start()
    
    def _perform_shap_analysis(self):
        """执行SHAP分析"""
        try:
            self.status_var.set("正在初始化SHAP解释器...")
            self.progress_var.set(20)
            
            # 初始化SHAP解释器
            success = self.shap_analyzer.initialize_explainer(
                self.current_model, self.current_X_test
            )
            
            if not success:
                self.status_var.set("SHAP解释器初始化失败")
                return
            
            self.progress_var.set(50)
            self.status_var.set("正在计算SHAP值...")
            
            # 计算SHAP值
            success = self.shap_analyzer.calculate_shap_values(self.current_X_test)
            
            if not success:
                self.status_var.set("SHAP值计算失败")
                return
            
            self.progress_var.set(80)
            self.status_var.set("正在生成图表...")
            
            # 生成图表
            self._refresh_plot()
            
            self.progress_var.set(100)
            self.status_var.set("SHAP分析完成")
            
            # 重置进度条
            self.parent.after(2000, lambda: self.progress_var.set(0))
            
        except Exception as e:
            self.error_handler.handle_error(e, "SHAP分析")
            self.status_var.set("SHAP分析失败")
            self.progress_var.set(0)
    
    def _refresh_plot(self):
        """刷新图表"""
        if self.shap_analyzer.shap_values is None:
            self._show_empty_plot()
            return
        
        try:
            self.ax.clear()
            
            if self.current_plot_type == "摘要图":
                fig = self.shap_analyzer.create_summary_plot(self.current_X_test)
            elif self.current_plot_type == "依赖图":
                fig = self.shap_analyzer.create_dependence_plot(
                    self.current_X_test, self.current_feature_idx
                )
            elif self.current_plot_type == "瀑布图":
                fig = self.shap_analyzer.create_waterfall_plot(
                    self.current_X_test, self.current_sample_idx
                )
            elif self.current_plot_type == "力图":
                fig = self.shap_analyzer.create_force_plot(
                    self.current_X_test, self.current_sample_idx
                )
            else:
                self._show_empty_plot()
                return
            
            if fig:
                # 将生成的图表复制到当前画布
                self.fig.clear()
                self.ax = self.fig.add_subplot(111)
                
                # 这里需要将SHAP生成的图表内容复制过来
                # 由于SHAP直接操作matplotlib，我们需要重新绘制
                self._redraw_shap_plot()
            else:
                self._show_empty_plot()
            
            self.canvas.draw()
            
        except Exception as e:
            self.error_handler.handle_error(e, "刷新SHAP图表")
            self._show_empty_plot()
    
    def _redraw_shap_plot(self):
        """重新绘制SHAP图表"""
        # 这是一个简化的实现，实际中需要根据SHAP的具体输出来处理
        self.ax.text(0.5, 0.5, f'SHAP {self.current_plot_type}\n(图表生成中...)', 
                    ha='center', va='center', transform=self.ax.transAxes,
                    fontsize=12, color='blue')
    
    def _save_plot(self):
        """保存当前图表"""
        if self.fig is None:
            messagebox.showwarning("警告", "没有可保存的图表")
            return
        
        filename = filedialog.asksaveasfilename(
            title="保存SHAP图表",
            defaultextension=".png",
            filetypes=[("PNG文件", "*.png"), ("PDF文件", "*.pdf"), ("所有文件", "*.*")]
        )
        
        if filename:
            try:
                self.fig.savefig(filename, dpi=300, bbox_inches='tight')
                messagebox.showinfo("成功", f"图表已保存到: {filename}")
            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {e}")
    
    def get_frame(self):
        """获取主框架"""
        return self.frame
