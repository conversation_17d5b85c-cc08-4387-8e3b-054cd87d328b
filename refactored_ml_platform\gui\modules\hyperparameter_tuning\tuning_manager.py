#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
超参数调优管理器
负责模型超参数的自动调优和优化
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, Any, Optional, List
import threading
import time
import pandas as pd
import numpy as np
import json

from ...core.base_gui import BaseGUI
from ...core.component_factory import get_component_factory
from ...core.event_manager import get_event_manager, EventTypes
from ...components.progress_widgets import ProgressWidget

# 导入算法模块
try:
    from ....algorithms import MODEL_TRAINERS
    from ....algorithms.hyperparameter_optimization import optimize_hyperparameters
    HAS_ALGORITHMS = True
except ImportError:
    MODEL_TRAINERS = {}
    HAS_ALGORITHMS = False


class HyperparameterTuningManager(BaseGUI):
    """超参数调优管理器类"""
    
    def __init__(self, parent):
        """初始化超参数调优管理器"""
        self.current_data = None
        self.preprocessed_data = None
        self.training_results = None
        self.tuning_results = {}
        self.selected_model = None
        self.tuning_config = {
            'method': 'tpe',  # TPE, random, grid
            'n_trials': 100,
            'cv_folds': 5,
            'scoring': 'accuracy',
            'timeout': 3600,  # 1小时
            'random_state': 42
        }
        self.is_tuning = False
        
        # 预定义的超参数空间
        self.hyperparameter_spaces = {
            'RandomForest': {
                'n_estimators': {'type': 'int', 'low': 50, 'high': 300, 'default': 100},
                'max_depth': {'type': 'int', 'low': 3, 'high': 20, 'default': 10},
                'min_samples_split': {'type': 'int', 'low': 2, 'high': 20, 'default': 2},
                'min_samples_leaf': {'type': 'int', 'low': 1, 'high': 10, 'default': 1},
                'max_features': {'type': 'categorical', 'choices': ['sqrt', 'log2', None], 'default': 'sqrt'}
            },
            'XGBoost': {
                'n_estimators': {'type': 'int', 'low': 50, 'high': 300, 'default': 100},
                'max_depth': {'type': 'int', 'low': 3, 'high': 10, 'default': 6},
                'learning_rate': {'type': 'float', 'low': 0.01, 'high': 0.3, 'default': 0.1},
                'subsample': {'type': 'float', 'low': 0.6, 'high': 1.0, 'default': 1.0},
                'colsample_bytree': {'type': 'float', 'low': 0.6, 'high': 1.0, 'default': 1.0}
            },
            'LightGBM': {
                'n_estimators': {'type': 'int', 'low': 50, 'high': 300, 'default': 100},
                'max_depth': {'type': 'int', 'low': 3, 'high': 15, 'default': -1},
                'learning_rate': {'type': 'float', 'low': 0.01, 'high': 0.3, 'default': 0.1},
                'num_leaves': {'type': 'int', 'low': 10, 'high': 300, 'default': 31},
                'feature_fraction': {'type': 'float', 'low': 0.4, 'high': 1.0, 'default': 1.0}
            },
            'SVM': {
                'C': {'type': 'float', 'low': 0.1, 'high': 100, 'default': 1.0},
                'gamma': {'type': 'categorical', 'choices': ['scale', 'auto'], 'default': 'scale'},
                'kernel': {'type': 'categorical', 'choices': ['rbf', 'linear', 'poly'], 'default': 'rbf'}
            },
            'LogisticRegression': {
                'C': {'type': 'float', 'low': 0.01, 'high': 100, 'default': 1.0},
                'penalty': {'type': 'categorical', 'choices': ['l1', 'l2', 'elasticnet'], 'default': 'l2'},
                'solver': {'type': 'categorical', 'choices': ['liblinear', 'saga'], 'default': 'liblinear'}
            },
            'KNN': {
                'n_neighbors': {'type': 'int', 'low': 3, 'high': 20, 'default': 5},
                'weights': {'type': 'categorical', 'choices': ['uniform', 'distance'], 'default': 'uniform'},
                'algorithm': {'type': 'categorical', 'choices': ['auto', 'ball_tree', 'kd_tree', 'brute'], 'default': 'auto'}
            }
        }
        
        super().__init__(parent)
        
        # 订阅相关事件
        self._bind_events()
    
    def _setup_ui(self):
        """设置UI界面"""
        factory = get_component_factory()
        
        # 主框架
        if self.parent:
            self.main_frame = factory.create_frame(self.parent)
            self.main_frame.pack(fill='both', expand=True, padx=5, pady=5)
        else:
            self.main_frame = None
            return
        
        # 创建主要内容区域
        self._create_main_content()
    
    def _create_main_content(self):
        """创建主要内容区域"""
        factory = get_component_factory()
        
        # 创建水平分割面板
        paned_window = ttk.PanedWindow(self.main_frame, orient=tk.HORIZONTAL)
        paned_window.pack(fill='both', expand=True)
        
        # 左侧配置面板
        self._create_config_panel(paned_window)
        
        # 右侧结果面板
        self._create_results_panel(paned_window)
        
        self.register_component('paned_window', paned_window)
    
    def _create_config_panel(self, parent):
        """创建左侧配置面板"""
        factory = get_component_factory()
        
        # 配置面板框架
        config_frame = factory.create_frame(parent)
        config_frame.pack(fill='both', expand=True)
        
        # 创建滚动区域
        canvas = tk.Canvas(config_frame, width=400)
        scrollbar = ttk.Scrollbar(config_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = factory.create_frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 状态信息
        self._create_status_section(scrollable_frame)
        
        # 模型选择
        self._create_model_selection_section(scrollable_frame)
        
        # 调优配置
        self._create_tuning_config_section(scrollable_frame)
        
        # 超参数空间配置
        self._create_hyperparameter_space_section(scrollable_frame)
        
        # 控制按钮
        self._create_control_buttons_section(scrollable_frame)
        
        # 将配置面板添加到分割窗口
        parent.add(config_frame, weight=1)
        
        self.register_component('config_frame', config_frame)
    
    def _create_status_section(self, parent):
        """创建状态信息区域"""
        factory = get_component_factory()
        
        status_frame = factory.create_labelframe(parent, text="🔧 调优状态")
        status_frame.pack(fill='x', padx=10, pady=5)
        
        self.status_label = factory.create_label(status_frame, text="等待模型训练完成...", style='info')
        self.status_label.pack(padx=10, pady=10)
        
        # 进度条
        self.progress_widget = ProgressWidget(status_frame, show_percentage=True)
        if self.progress_widget.main_frame:
            self.progress_widget.main_frame.pack(fill='x', padx=10, pady=(0, 10))
    
    def _create_model_selection_section(self, parent):
        """创建模型选择区域"""
        factory = get_component_factory()
        
        model_frame = factory.create_labelframe(parent, text="🎯 模型选择")
        model_frame.pack(fill='x', padx=10, pady=5)
        
        # 说明文本
        info_label = factory.create_label(
            model_frame, 
            text="选择要进行超参数调优的模型:",
            style='secondary'
        )
        info_label.pack(padx=10, pady=5, anchor='w')
        
        # 模型选择下拉框
        self.selected_model_var = tk.StringVar()
        self.model_combobox = ttk.Combobox(model_frame, textvariable=self.selected_model_var, 
                                         state='readonly', width=30)
        self.model_combobox.pack(padx=10, pady=10)
        self.model_combobox.bind('<<ComboboxSelected>>', self._on_model_selected)
        
        # 模型信息显示
        self.model_info_label = factory.create_label(
            model_frame,
            text="请先完成模型训练",
            style='secondary'
        )
        self.model_info_label.pack(padx=10, pady=5)
    
    def _create_tuning_config_section(self, parent):
        """创建调优配置区域"""
        factory = get_component_factory()
        
        config_frame = factory.create_labelframe(parent, text="⚙️ 调优配置")
        config_frame.pack(fill='x', padx=10, pady=5)
        
        # 调优方法
        method_frame = factory.create_frame(config_frame)
        method_frame.pack(fill='x', padx=10, pady=5)
        
        factory.create_label(method_frame, text="调优方法:").pack(side='left')
        self.tuning_method_var = tk.StringVar(value='tpe')
        method_combo = ttk.Combobox(method_frame, textvariable=self.tuning_method_var,
                                  values=['tpe', 'random', 'grid'], state='readonly', width=15)
        method_combo.pack(side='right')
        
        # 试验次数
        trials_frame = factory.create_frame(config_frame)
        trials_frame.pack(fill='x', padx=10, pady=5)
        
        factory.create_label(trials_frame, text="试验次数:").pack(side='left')
        self.n_trials_var = tk.IntVar(value=100)
        trials_spin = tk.Spinbox(trials_frame, from_=10, to=1000, width=10, textvariable=self.n_trials_var)
        trials_spin.pack(side='right')
        
        # 交叉验证折数
        cv_frame = factory.create_frame(config_frame)
        cv_frame.pack(fill='x', padx=10, pady=5)
        
        factory.create_label(cv_frame, text="交叉验证折数:").pack(side='left')
        self.cv_folds_var = tk.IntVar(value=5)
        cv_spin = tk.Spinbox(cv_frame, from_=3, to=10, width=10, textvariable=self.cv_folds_var)
        cv_spin.pack(side='right')
        
        # 评分指标
        scoring_frame = factory.create_frame(config_frame)
        scoring_frame.pack(fill='x', padx=10, pady=5)
        
        factory.create_label(scoring_frame, text="评分指标:").pack(side='left')
        self.scoring_var = tk.StringVar(value='accuracy')
        scoring_combo = ttk.Combobox(scoring_frame, textvariable=self.scoring_var,
                                   values=['accuracy', 'f1', 'precision', 'recall', 'roc_auc'],
                                   state='readonly', width=15)
        scoring_combo.pack(side='right')
        
        # 超时时间
        timeout_frame = factory.create_frame(config_frame)
        timeout_frame.pack(fill='x', padx=10, pady=5)
        
        factory.create_label(timeout_frame, text="超时时间(秒):").pack(side='left')
        self.timeout_var = tk.IntVar(value=3600)
        timeout_entry = factory.create_entry(timeout_frame, textvariable=self.timeout_var, width=10)
        timeout_entry.pack(side='right')

    def _create_hyperparameter_space_section(self, parent):
        """创建超参数空间配置区域"""
        factory = get_component_factory()

        space_frame = factory.create_labelframe(parent, text="📊 超参数空间")
        space_frame.pack(fill='x', padx=10, pady=5)

        # 超参数配置显示区域
        self.hyperparameter_display_frame = factory.create_frame(space_frame)
        self.hyperparameter_display_frame.pack(fill='x', padx=10, pady=10)

        # 占位符文本
        self.no_model_selected_label = factory.create_label(
            self.hyperparameter_display_frame,
            text="请先选择要调优的模型",
            style='secondary'
        )
        self.no_model_selected_label.pack(pady=10)

    def _create_control_buttons_section(self, parent):
        """创建控制按钮区域"""
        factory = get_component_factory()

        button_frame = factory.create_frame(parent)
        button_frame.pack(fill='x', padx=10, pady=10)

        # 开始调优按钮
        self.tuning_button = factory.create_button(
            button_frame,
            text="🚀 开始超参数调优",
            command=self._start_tuning,
            style='primary'
        )
        self.tuning_button.pack(fill='x', pady=(0, 5))
        self.tuning_button.config(state='disabled')

        # 停止调优按钮
        self.stop_button = factory.create_button(
            button_frame,
            text="⏹️ 停止调优",
            command=self._stop_tuning,
            style='danger'
        )
        self.stop_button.pack(fill='x', pady=(0, 5))
        self.stop_button.config(state='disabled')

        # 重置参数按钮
        reset_button = factory.create_button(
            button_frame,
            text="🔄 重置参数",
            command=self._reset_parameters,
            style='secondary'
        )
        reset_button.pack(fill='x', pady=(0, 5))

        # 清空结果按钮
        clear_button = factory.create_button(
            button_frame,
            text="🗑️ 清空结果",
            command=self.clear_results,
            style='secondary'
        )
        clear_button.pack(fill='x')

    def _create_results_panel(self, parent):
        """创建右侧结果面板"""
        factory = get_component_factory()

        # 结果面板框架
        results_frame = factory.create_frame(parent)
        results_frame.pack(fill='both', expand=True)

        # 创建标签页
        self.results_notebook = factory.create_notebook(results_frame)
        self.results_notebook.pack(fill='both', expand=True, padx=5, pady=5)

        # 调优日志标签页
        self._create_tuning_log_tab()

        # 调优结果标签页
        self._create_tuning_results_tab()

        # 参数重要性标签页
        self._create_parameter_importance_tab()

        # 优化历史标签页
        self._create_optimization_history_tab()

        # 将结果面板添加到分割窗口
        parent.add(results_frame, weight=2)

        self.register_component('results_frame', results_frame)

    def _create_tuning_log_tab(self):
        """创建调优日志标签页"""
        factory = get_component_factory()

        log_frame = factory.create_frame(self.results_notebook)
        self.results_notebook.add(log_frame, text="📋 调优日志")

        # 日志文本区域
        self.log_text = tk.Text(log_frame, wrap=tk.WORD, font=('Consolas', 9))
        log_scrollbar = ttk.Scrollbar(log_frame, orient='vertical', command=self.log_text.yview)
        self.log_text.config(yscrollcommand=log_scrollbar.set)

        self.log_text.pack(side='left', fill='both', expand=True, padx=(5, 0), pady=5)
        log_scrollbar.pack(side='right', fill='y', padx=(0, 5), pady=5)

        self.log_text.insert('1.0', "超参数调优日志将在这里显示...\n")
        self.log_text.config(state='disabled')

    def _create_tuning_results_tab(self):
        """创建调优结果标签页"""
        factory = get_component_factory()

        results_frame = factory.create_frame(self.results_notebook)
        self.results_notebook.add(results_frame, text="📊 调优结果")

        # 最佳参数显示
        best_params_frame = factory.create_labelframe(results_frame, text="🏆 最佳参数")
        best_params_frame.pack(fill='x', padx=5, pady=5)

        self.best_params_text = tk.Text(best_params_frame, height=8, wrap=tk.WORD, font=('Consolas', 9))
        params_scrollbar = ttk.Scrollbar(best_params_frame, orient='vertical', command=self.best_params_text.yview)
        self.best_params_text.config(yscrollcommand=params_scrollbar.set)

        self.best_params_text.pack(side='left', fill='both', expand=True, padx=(5, 0), pady=5)
        params_scrollbar.pack(side='right', fill='y', padx=(0, 5), pady=5)

        # 试验历史表格
        history_frame = factory.create_labelframe(results_frame, text="📈 试验历史")
        history_frame.pack(fill='both', expand=True, padx=5, pady=5)

        columns = ('试验', '分数', '参数')
        self.history_tree = ttk.Treeview(history_frame, columns=columns, show='headings', height=10)

        # 设置列标题
        for col in columns:
            self.history_tree.heading(col, text=col)
            if col == '参数':
                self.history_tree.column(col, width=300, anchor='w')
            else:
                self.history_tree.column(col, width=80, anchor='center')

        # 滚动条
        history_scrollbar = ttk.Scrollbar(history_frame, orient='vertical', command=self.history_tree.yview)
        self.history_tree.config(yscrollcommand=history_scrollbar.set)

        self.history_tree.pack(side='left', fill='both', expand=True, padx=(5, 0), pady=5)
        history_scrollbar.pack(side='right', fill='y', padx=(0, 5), pady=5)

    def _create_parameter_importance_tab(self):
        """创建参数重要性标签页"""
        factory = get_component_factory()

        importance_frame = factory.create_frame(self.results_notebook)
        self.results_notebook.add(importance_frame, text="⭐ 参数重要性")

        # 占位符内容
        placeholder_label = factory.create_label(
            importance_frame,
            text="参数重要性分析将在调优完成后显示\n\n包括：\n• 各参数对性能的影响程度\n• 参数敏感性分析\n• 参数相关性分析",
            style='secondary'
        )
        placeholder_label.pack(expand=True)

    def _create_optimization_history_tab(self):
        """创建优化历史标签页"""
        factory = get_component_factory()

        history_frame = factory.create_frame(self.results_notebook)
        self.results_notebook.add(history_frame, text="📈 优化历史")

        # 占位符内容
        placeholder_label = factory.create_label(
            history_frame,
            text="优化历史图表将在调优完成后显示\n\n包括：\n• 优化过程曲线\n• 参数收敛分析\n• 最佳分数变化趋势\n• 试验分布图",
            style='secondary'
        )
        placeholder_label.pack(expand=True)

    def _on_model_selected(self, event):
        """模型选择事件处理"""
        selected_model = self.selected_model_var.get()
        if selected_model and selected_model in self.training_results:
            self.selected_model = selected_model

            # 更新模型信息显示
            result = self.training_results[selected_model]
            metrics = result.get('metrics', {})
            info_text = f"当前性能: 准确率 {metrics.get('accuracy', 0):.3f}, F1 {metrics.get('f1', 0):.3f}"
            self.model_info_label.config(text=info_text)

            # 更新超参数空间显示
            self._update_hyperparameter_display(selected_model)

            # 启用调优按钮
            self.tuning_button.config(state='normal')

    def _update_hyperparameter_display(self, model_name):
        """更新超参数空间显示"""
        # 清空现有显示
        for widget in self.hyperparameter_display_frame.winfo_children():
            widget.destroy()

        if model_name not in self.hyperparameter_spaces:
            factory = get_component_factory()
            no_config_label = factory.create_label(
                self.hyperparameter_display_frame,
                text=f"暂不支持 {model_name} 的超参数调优",
                style='secondary'
            )
            no_config_label.pack(pady=10)
            return

        # 显示超参数配置
        factory = get_component_factory()
        space_config = self.hyperparameter_spaces[model_name]

        for param_name, param_config in space_config.items():
            param_frame = factory.create_frame(self.hyperparameter_display_frame)
            param_frame.pack(fill='x', pady=2)

            # 参数名称
            name_label = factory.create_label(param_frame, text=f"{param_name}:")
            name_label.pack(side='left', padx=(0, 10))

            # 参数配置信息
            if param_config['type'] == 'int':
                info_text = f"整数 [{param_config['low']}, {param_config['high']}], 默认: {param_config['default']}"
            elif param_config['type'] == 'float':
                info_text = f"浮点 [{param_config['low']:.3f}, {param_config['high']:.3f}], 默认: {param_config['default']}"
            elif param_config['type'] == 'categorical':
                info_text = f"选择 {param_config['choices']}, 默认: {param_config['default']}"
            else:
                info_text = f"类型: {param_config['type']}"

            info_label = factory.create_label(param_frame, text=info_text, style='secondary')
            info_label.pack(side='left')

    def _start_tuning(self):
        """开始超参数调优"""
        if not self.selected_model:
            self.show_warning("警告", "请先选择要调优的模型！")
            return

        if not self.preprocessed_data:
            self.show_warning("警告", "请先完成数据预处理！")
            return

        # 更新调优配置
        self.tuning_config.update({
            'method': self.tuning_method_var.get(),
            'n_trials': self.n_trials_var.get(),
            'cv_folds': self.cv_folds_var.get(),
            'scoring': self.scoring_var.get(),
            'timeout': self.timeout_var.get()
        })

        # 更新UI状态
        self.is_tuning = True
        self.status_label.config(text=f"正在调优 {self.selected_model}...")
        self.tuning_button.config(state='disabled')
        self.stop_button.config(state='normal')

        # 清空之前的结果
        self._clear_results_display()

        # 开始调优
        self._run_tuning()

    def _stop_tuning(self):
        """停止超参数调优"""
        self.is_tuning = False
        self.status_label.config(text="超参数调优已停止")
        self.tuning_button.config(state='normal')
        self.stop_button.config(state='disabled')
        self._log_message("超参数调优已被用户停止")

    def _run_tuning(self):
        """运行超参数调优过程"""
        def tuning():
            try:
                self._log_message(f"开始调优 {self.selected_model}")
                self._log_message(f"调优配置: {self.tuning_config}")

                # 模拟调优过程
                n_trials = self.tuning_config['n_trials']
                best_score = 0
                best_params = {}
                trial_history = []

                for trial in range(n_trials):
                    if not self.is_tuning:
                        break

                    # 更新进度
                    progress = (trial / n_trials) * 100
                    if self.main_frame:
                        self.main_frame.after(0, lambda p=progress: self.progress_widget.set_progress(p))

                    # 模拟试验
                    if HAS_ALGORITHMS and self.selected_model in self.hyperparameter_spaces:
                        # 这里可以调用真实的超参数优化算法
                        # result = optimize_hyperparameters(...)
                        pass

                    # 模拟试验结果
                    time.sleep(0.1)  # 模拟调优时间

                    # 生成随机参数和分数
                    trial_score = np.random.uniform(0.7, 0.95)
                    trial_params = self._generate_random_params(self.selected_model)

                    trial_history.append({
                        'trial': trial + 1,
                        'score': trial_score,
                        'params': trial_params
                    })

                    if trial_score > best_score:
                        best_score = trial_score
                        best_params = trial_params.copy()

                        # 记录新的最佳结果
                        self._log_message(f"试验 {trial + 1}: 新的最佳分数 {trial_score:.4f}")

                    # 每10次试验记录一次进度
                    if (trial + 1) % 10 == 0:
                        self._log_message(f"已完成 {trial + 1}/{n_trials} 次试验，当前最佳分数: {best_score:.4f}")

                # 调优完成
                tuning_result = {
                    'model': self.selected_model,
                    'best_score': best_score,
                    'best_params': best_params,
                    'trial_history': trial_history,
                    'config': self.tuning_config.copy()
                }

                # 在主线程中更新UI
                if self.is_tuning and self.main_frame:
                    self.main_frame.after(0, lambda: self._tuning_completed(tuning_result))

            except Exception as e:
                if self.main_frame:
                    self.main_frame.after(0, lambda: self._tuning_failed(str(e)))

        # 在后台线程中运行调优
        tuning_thread = threading.Thread(target=tuning)
        tuning_thread.daemon = True
        tuning_thread.start()

    def _generate_random_params(self, model_name):
        """生成随机参数（用于模拟）"""
        if model_name not in self.hyperparameter_spaces:
            return {}

        params = {}
        space_config = self.hyperparameter_spaces[model_name]

        for param_name, param_config in space_config.items():
            if param_config['type'] == 'int':
                params[param_name] = np.random.randint(param_config['low'], param_config['high'] + 1)
            elif param_config['type'] == 'float':
                params[param_name] = np.random.uniform(param_config['low'], param_config['high'])
            elif param_config['type'] == 'categorical':
                params[param_name] = np.random.choice(param_config['choices'])

        return params

    def _log_message(self, message):
        """添加日志消息"""
        if hasattr(self, 'log_text'):
            timestamp = time.strftime("%H:%M:%S")
            log_entry = f"[{timestamp}] {message}\n"

            self.log_text.config(state='normal')
            self.log_text.insert('end', log_entry)
            self.log_text.see('end')
            self.log_text.config(state='disabled')

    def _clear_results_display(self):
        """清空结果显示"""
        # 清空最佳参数显示
        if hasattr(self, 'best_params_text'):
            self.best_params_text.delete('1.0', 'end')

        # 清空历史表格
        if hasattr(self, 'history_tree'):
            for item in self.history_tree.get_children():
                self.history_tree.delete(item)

        # 重置进度条
        if hasattr(self, 'progress_widget'):
            self.progress_widget.set_progress(0)

    def _tuning_completed(self, result):
        """调优完成处理"""
        self.tuning_results[result['model']] = result
        self.is_tuning = False

        # 更新UI状态
        self.status_label.config(text=f"✅ 调优完成！最佳分数: {result['best_score']:.4f}")
        self.tuning_button.config(state='normal')
        self.stop_button.config(state='disabled')
        self.progress_widget.set_progress(100)

        # 更新结果显示
        self._update_results_display(result)

        # 记录完成信息
        self._log_message(f"调优完成！最佳分数: {result['best_score']:.4f}")
        self._log_message(f"最佳参数: {result['best_params']}")

        # 发布调优完成事件
        event_manager = get_event_manager()
        event_manager.publish(EventTypes.MODEL_TRAINED, {
            'results': {f"{result['model']}_tuned": {
                'metrics': {'accuracy': result['best_score']},
                'params': result['best_params'],
                'tuning_info': result
            }},
            'type': 'hyperparameter_tuning'
        })

    def _tuning_failed(self, error_message):
        """调优失败处理"""
        self.is_tuning = False
        self.status_label.config(text=f"❌ 调优失败: {error_message}")
        self.tuning_button.config(state='normal')
        self.stop_button.config(state='disabled')

        self._log_message(f"调优失败: {error_message}")
        self.show_error("调优失败", f"超参数调优过程中出现错误:\n{error_message}")

    def _update_results_display(self, result):
        """更新结果显示"""
        # 更新最佳参数显示
        if hasattr(self, 'best_params_text'):
            params_text = f"模型: {result['model']}\n"
            params_text += f"最佳分数: {result['best_score']:.4f}\n"
            params_text += f"调优方法: {result['config']['method']}\n"
            params_text += f"试验次数: {len(result['trial_history'])}\n\n"
            params_text += "最佳参数:\n"

            for param_name, param_value in result['best_params'].items():
                if isinstance(param_value, float):
                    params_text += f"  {param_name}: {param_value:.4f}\n"
                else:
                    params_text += f"  {param_name}: {param_value}\n"

            self.best_params_text.delete('1.0', 'end')
            self.best_params_text.insert('1.0', params_text)

        # 更新历史表格（显示最后20次试验）
        if hasattr(self, 'history_tree'):
            # 清空现有数据
            for item in self.history_tree.get_children():
                self.history_tree.delete(item)

            # 添加试验历史（最后20次）
            history = result['trial_history'][-20:]  # 只显示最后20次
            for trial_data in history:
                params_str = ', '.join([f"{k}={v}" for k, v in trial_data['params'].items()])
                if len(params_str) > 50:
                    params_str = params_str[:47] + "..."

                values = (
                    trial_data['trial'],
                    f"{trial_data['score']:.4f}",
                    params_str
                )

                self.history_tree.insert('', 'end', values=values)

    def _reset_parameters(self):
        """重置参数到默认值"""
        self.tuning_method_var.set('tpe')
        self.n_trials_var.set(100)
        self.cv_folds_var.set(5)
        self.scoring_var.set('accuracy')
        self.timeout_var.set(3600)

        self._log_message("参数已重置为默认值")

    def _bind_events(self):
        """绑定事件"""
        event_manager = get_event_manager()
        event_manager.subscribe(EventTypes.DATA_LOADED, self._on_data_loaded)
        event_manager.subscribe(EventTypes.DATA_PREPROCESSED, self._on_data_preprocessed)
        event_manager.subscribe(EventTypes.MODEL_TRAINED, self._on_model_trained)

    def _on_data_loaded(self, event_data):
        """数据加载事件处理"""
        try:
            if event_data and 'data' in event_data:
                self.current_data = event_data['data']
                self.preprocessed_data = None  # 重置预处理数据

                # 更新状态
                rows, cols = self.current_data.shape
                self._log_message(f"数据已加载: {rows} 行, {cols} 列")
                self.logger.info(f"超参数调优模块已加载数据: {self.current_data.shape}")
        except Exception as e:
            self.logger.error(f"处理数据加载事件时出错: {e}")
            self._log_message(f"数据加载失败: {e}")

    def _on_data_preprocessed(self, event_data):
        """数据预处理事件处理"""
        try:
            if event_data and all(key in event_data for key in ['X_train', 'X_test', 'y_train', 'y_test']):
                # 保存预处理后的数据
                self.preprocessed_data = event_data

                train_size = event_data['X_train'].shape[0]
                test_size = event_data['X_test'].shape[0]
                feature_count = event_data['X_train'].shape[1]

                self._log_message(f"数据已预处理: 训练集 {train_size} 行, 测试集 {test_size} 行, 特征 {feature_count} 个")
                self.logger.info(f"超参数调优模块已接收预处理数据")

            elif event_data and 'data' in event_data:
                # 如果只有原始数据
                self.current_data = event_data['data']
                self._log_message(f"数据已预处理: {self.current_data.shape[0]} 行, {self.current_data.shape[1]} 列")
        except Exception as e:
            self.logger.error(f"处理数据预处理事件时出错: {e}")
            self._log_message(f"数据预处理失败: {e}")

    def _on_model_trained(self, event_data):
        """模型训练完成事件处理"""
        try:
            if event_data and 'results' in event_data:
                # 检查是否是调优结果
                if event_data.get('type') == 'hyperparameter_tuning':
                    return  # 忽略调优结果，避免循环

                self.training_results = event_data['results']

                # 更新模型选择下拉框
                model_names = [name for name in self.training_results.keys()
                             if name in self.hyperparameter_spaces]
                self.model_combobox['values'] = model_names

                if model_names:
                    self.model_combobox.set(model_names[0])
                    self._on_model_selected(None)  # 触发模型选择事件

                # 更新状态
                model_count = len(self.training_results)
                supported_count = len(model_names)
                self.status_label.config(text=f"✅ 已完成 {model_count} 个模型训练，支持调优 {supported_count} 个")

                self._log_message(f"接收到 {model_count} 个训练完成的模型，其中 {supported_count} 个支持超参数调优")
                self.logger.info(f"超参数调优模块已接收训练结果: {model_count} 个模型")
        except Exception as e:
            self.logger.error(f"处理模型训练事件时出错: {e}")
            self._log_message(f"处理模型训练结果失败: {e}")

    def clear_results(self):
        """清空所有结果"""
        self.tuning_results = {}
        self.selected_model = None

        # 清空结果显示
        self._clear_results_display()

        # 清空日志
        if hasattr(self, 'log_text'):
            self.log_text.config(state='normal')
            self.log_text.delete('1.0', 'end')
            self.log_text.insert('1.0', "超参数调优日志将在这里显示...\n")
            self.log_text.config(state='disabled')

        # 重置模型选择
        if hasattr(self, 'model_combobox'):
            self.model_combobox.set('')

        # 重置超参数显示
        if hasattr(self, 'hyperparameter_display_frame'):
            for widget in self.hyperparameter_display_frame.winfo_children():
                widget.destroy()

            factory = get_component_factory()
            self.no_model_selected_label = factory.create_label(
                self.hyperparameter_display_frame,
                text="请先选择要调优的模型",
                style='secondary'
            )
            self.no_model_selected_label.pack(pady=10)

        # 重置状态
        if hasattr(self, 'status_label'):
            self.status_label.config(text="等待模型训练完成...")

        # 禁用按钮
        if hasattr(self, 'tuning_button'):
            self.tuning_button.config(state='disabled')

        self._log_message("所有结果已清空")

    def get_tuning_results(self) -> Dict[str, Any]:
        """获取调优结果"""
        return self.tuning_results.copy()

    def is_tuning_active(self) -> bool:
        """检查是否正在进行调优"""
        return self.is_tuning
