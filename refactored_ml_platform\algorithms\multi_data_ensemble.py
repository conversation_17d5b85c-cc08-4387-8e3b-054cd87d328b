#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多数据源集成学习模块
提供多数据源集成学习功能
"""

import pandas as pd
import numpy as np
from sklearn.ensemble import VotingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import accuracy_score, roc_auc_score
import joblib
from pathlib import Path
import time
import json

# 导入配置
try:
    from .config import MULTI_DATA_CACHE_PATH, RANDOM_SEED
    from .logger import get_logger
    from .data_preprocessing import load_and_preprocess_data
    from .model_training import MODEL_TRAINERS
except ImportError:
    # 使用默认配置
    import os
    PROJECT_ROOT = Path(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    MULTI_DATA_CACHE_PATH = PROJECT_ROOT / 'multi_data_cache'
    MULTI_DATA_CACHE_PATH.mkdir(parents=True, exist_ok=True)
    RANDOM_SEED = 42
    
    import logging
    def get_logger(name):
        return logging.getLogger(name)
    
    def load_and_preprocess_data(data_path, **kwargs):
        df = pd.read_csv(data_path)
        target_col = df.columns[-1]
        X = df.drop(columns=[target_col])
        y = df[target_col]
        return {
            'X_train': X[:int(0.8*len(X))],
            'X_test': X[int(0.8*len(X)):],
            'y_train': y[:int(0.8*len(y))],
            'y_test': y[int(0.8*len(y)):],
            'feature_names': list(X.columns),
            'data_path': data_path
        }
    
    MODEL_TRAINERS = {}

logger = get_logger(__name__)

class MultiDataEnsemble:
    """
    多数据源集成学习类
    """
    
    def __init__(self, ensemble_methods=None, data_strategies=None):
        """
        初始化多数据源集成学习
        
        Args:
            ensemble_methods: 集成方法列表
            data_strategies: 数据策略列表
        """
        self.ensemble_methods = ensemble_methods or ['voting', 'stacking']
        self.data_strategies = data_strategies or ['unified', 'original']
        self.trained_models = {}
        self.ensemble_models = {}
        self.datasets = {}
    
    def add_dataset(self, data_path, model_name, dataset_name=None):
        """
        添加数据集
        
        Args:
            data_path: 数据文件路径
            model_name: 对应的模型名称
            dataset_name: 数据集名称
        """
        if dataset_name is None:
            dataset_name = f"dataset_{len(self.datasets)}"
        
        logger.info(f"添加数据集: {dataset_name} -> {model_name}")
        
        # 加载和预处理数据
        dataset = load_and_preprocess_data(data_path)
        dataset['model_name'] = model_name
        dataset['dataset_name'] = dataset_name
        
        self.datasets[dataset_name] = dataset
    
    def train_base_models(self):
        """训练基础模型"""
        logger.info("开始训练基础模型")
        
        for dataset_name, dataset in self.datasets.items():
            model_name = dataset['model_name']
            
            if model_name in MODEL_TRAINERS:
                logger.info(f"训练模型 {model_name} 在数据集 {dataset_name}")
                
                trainer = MODEL_TRAINERS[model_name]
                result = trainer.train_and_evaluate(
                    dataset['X_train'], dataset['y_train'],
                    dataset['X_test'], dataset['y_test']
                )
                
                self.trained_models[f"{dataset_name}_{model_name}"] = result
                logger.info(f"模型训练完成，准确率: {result['metrics']['accuracy']:.4f}")
            else:
                logger.warning(f"未找到模型训练器: {model_name}")
    
    def create_ensemble_models(self):
        """创建集成模型"""
        logger.info("开始创建集成模型")
        
        for method in self.ensemble_methods:
            for strategy in self.data_strategies:
                ensemble_name = f"{method}_{strategy}"
                logger.info(f"创建集成模型: {ensemble_name}")
                
                if method == 'voting':
                    ensemble_model = self._create_voting_ensemble(strategy)
                elif method == 'stacking':
                    ensemble_model = self._create_stacking_ensemble(strategy)
                else:
                    logger.warning(f"不支持的集成方法: {method}")
                    continue
                
                self.ensemble_models[ensemble_name] = ensemble_model
    
    def _create_voting_ensemble(self, strategy):
        """创建投票集成模型"""
        estimators = []
        
        for key, result in self.trained_models.items():
            model = result['model']
            estimators.append((key, model))
        
        if not estimators:
            logger.warning("没有可用的基础模型")
            return None
        
        ensemble = VotingClassifier(
            estimators=estimators,
            voting='soft'
        )
        
        return ensemble
    
    def _create_stacking_ensemble(self, strategy):
        """创建堆叠集成模型"""
        from sklearn.ensemble import StackingClassifier
        
        estimators = []
        
        for key, result in self.trained_models.items():
            model = result['model']
            estimators.append((key, model))
        
        if not estimators:
            logger.warning("没有可用的基础模型")
            return None
        
        ensemble = StackingClassifier(
            estimators=estimators,
            final_estimator=LogisticRegression(random_state=RANDOM_SEED),
            cv=5
        )
        
        return ensemble
    
    def evaluate_ensembles(self, test_data_path=None):
        """评估集成模型"""
        logger.info("开始评估集成模型")
        
        results = {}
        
        # 如果提供了测试数据，使用测试数据评估
        if test_data_path:
            test_dataset = load_and_preprocess_data(test_data_path)
            X_test = test_dataset['X_test']
            y_test = test_dataset['y_test']
        else:
            # 使用第一个数据集的测试数据
            first_dataset = list(self.datasets.values())[0]
            X_test = first_dataset['X_test']
            y_test = first_dataset['y_test']
        
        for ensemble_name, ensemble_model in self.ensemble_models.items():
            if ensemble_model is None:
                continue
            
            logger.info(f"评估集成模型: {ensemble_name}")
            
            try:
                # 训练集成模型（使用第一个数据集的训练数据）
                first_dataset = list(self.datasets.values())[0]
                X_train = first_dataset['X_train']
                y_train = first_dataset['y_train']
                
                ensemble_model.fit(X_train, y_train)
                
                # 预测
                y_pred = ensemble_model.predict(X_test)
                y_pred_proba = ensemble_model.predict_proba(X_test)[:, 1]
                
                # 计算指标
                accuracy = accuracy_score(y_test, y_pred)
                auc = roc_auc_score(y_test, y_pred_proba)
                
                results[ensemble_name] = {
                    'accuracy': accuracy,
                    'auc': auc,
                    'y_pred': y_pred.tolist(),
                    'y_pred_proba': y_pred_proba.tolist()
                }
                
                logger.info(f"集成模型 {ensemble_name} - 准确率: {accuracy:.4f}, AUC: {auc:.4f}")
                
            except Exception as e:
                logger.error(f"评估集成模型 {ensemble_name} 时出错: {e}")
                results[ensemble_name] = {"error": str(e)}
        
        return results
    
    def save_results(self, results, save_path=None):
        """保存结果"""
        if save_path is None:
            timestamp = int(time.time())
            save_path = MULTI_DATA_CACHE_PATH / f"multi_data_ensemble_{timestamp}.json"
        
        # 确保目录存在
        save_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 保存为JSON文件
        with open(save_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        logger.info(f"多数据源集成结果已保存到: {save_path}")
        return save_path

def run_multi_data_ensemble_pipeline(model_data_mapping, ensemble_methods=None,
                                    ensemble_data_strategies=None, target_data_path=None,
                                    feature_names=None, enable_shap=True,
                                    feature_selection=False, feature_selection_method='combined',
                                    k=None):
    """
    运行多数据源集成学习流水线
    
    Args:
        model_data_mapping: 模型-数据映射字典
        ensemble_methods: 集成方法列表
        ensemble_data_strategies: 数据策略列表
        target_data_path: 目标数据路径
        feature_names: 特征名称列表
        enable_shap: 是否启用SHAP分析
        feature_selection: 是否进行特征选择
        feature_selection_method: 特征选择方法
        k: 特征选择数量
        
    Returns:
        dict: 多数据源集成结果
    """
    logger.info("开始多数据源集成学习流水线")
    
    # 创建多数据源集成学习器
    ensemble_learner = MultiDataEnsemble(ensemble_methods, ensemble_data_strategies)
    
    # 添加数据集
    for model_name, data_path in model_data_mapping.items():
        ensemble_learner.add_dataset(data_path, model_name)
    
    # 训练基础模型
    ensemble_learner.train_base_models()
    
    # 创建集成模型
    ensemble_learner.create_ensemble_models()
    
    # 评估集成模型
    results = ensemble_learner.evaluate_ensembles(target_data_path)
    
    # 保存结果
    ensemble_learner.save_results(results)
    
    logger.info("多数据源集成学习流水线完成")
    return results
