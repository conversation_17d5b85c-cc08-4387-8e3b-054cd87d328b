2025-08-07 12:44:38,892 - gui.core.config_manager - INFO - 配置文件不存在，使用默认配置
2025-08-07 12:44:38,894 - gui.core.config_manager - INFO - 配置已保存到文件: D:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 12:44:39,266 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-07 12:44:39,270 - __main__ - INFO - 启动重构版机器学习平台
2025-08-07 12:45:12,143 - gui.core.config_manager - INFO - 配置已保存到文件: D:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 12:45:12,143 - __main__ - INFO - 应用程序正在退出
2025-08-07 12:45:17,246 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 12:45:17,588 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-07 12:45:17,588 - __main__ - INFO - 启动重构版机器学习平台
2025-08-07 12:45:36,304 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 12:45:36,304 - __main__ - INFO - 应用程序正在退出
2025-08-07 12:59:07,306 - __main__ - INFO - 项目路径: D:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-07 12:59:07,306 - gui.core.config_manager - INFO - 配置已从文件加载: D:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 13:23:41,982 - __main__ - INFO - 项目路径: d:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-07 13:23:41,983 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 13:23:42,385 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-07 13:23:42,385 - __main__ - INFO - 启动GUI主循环
2025-08-07 13:24:41,868 - __main__ - INFO - 应用程序正在退出
2025-08-07 13:24:41,869 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 13:24:45,975 - __main__ - INFO - 项目路径: d:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-07 13:24:45,976 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 13:24:46,405 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-07 13:24:46,405 - __main__ - INFO - 启动GUI主循环
2025-08-07 13:25:47,748 - __main__ - INFO - 应用程序正在退出
2025-08-07 13:25:47,749 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 13:27:44,673 - __main__ - INFO - 项目路径: d:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-07 13:27:44,674 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 13:27:45,092 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-07 13:27:45,092 - __main__ - INFO - 启动GUI主循环
2025-08-07 13:27:55,804 - __main__ - INFO - 应用程序正在退出
2025-08-07 13:27:55,805 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 13:38:18,229 - __main__ - INFO - 项目路径: d:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-07 13:38:18,230 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 13:38:18,649 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-07 13:38:18,649 - __main__ - INFO - 启动GUI主循环
2025-08-07 13:40:11,729 - __main__ - INFO - 应用程序正在退出
2025-08-07 13:40:11,730 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 13:43:04,006 - __main__ - INFO - 项目路径: D:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-07 13:43:04,007 - gui.core.config_manager - INFO - 配置已从文件加载: D:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 13:43:36,732 - __main__ - INFO - 项目路径: D:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-07 13:43:36,733 - gui.core.config_manager - INFO - 配置已从文件加载: D:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 13:43:37,226 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-07 13:43:37,226 - __main__ - INFO - 启动GUI主循环
2025-08-07 13:45:21,410 - __main__ - INFO - 应用程序正在退出
2025-08-07 13:45:21,411 - gui.core.config_manager - INFO - 配置已保存到文件: D:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 13:49:03,468 - __main__ - INFO - 项目路径: D:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-07 13:49:03,469 - gui.core.config_manager - INFO - 配置已从文件加载: D:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 13:51:39,282 - __main__ - INFO - 项目路径: D:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-07 13:51:39,282 - gui.core.config_manager - INFO - 配置已从文件加载: D:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 13:51:39,828 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-07 13:51:39,828 - __main__ - INFO - 启动GUI主循环
2025-08-07 13:53:15,328 - __main__ - INFO - 项目路径: d:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-07 13:53:15,328 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 13:53:15,928 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-07 13:53:15,928 - __main__ - INFO - 启动GUI主循环
2025-08-07 13:53:31,273 - ProjectHistoryTab - INFO - 添加历史记录: 数据加载: C:/Users/<USER>/Desktop/parameters/0807/nodule2.csv
2025-08-07 13:53:32,650 - ModelTrainerTab - INFO - 接收到数据: (197, 9)
2025-08-07 13:53:32,652 - DataVisualizationTab - INFO - 数值列: 9, 分类列: 0
2025-08-07 13:53:32,663 - DataVisualizationTab - INFO - 接收到数据: (197, 9)
2025-08-07 13:53:32,663 - ProjectHistoryTab - INFO - 添加历史记录: 数据加载: C:/Users/<USER>/Desktop/parameters/0807/nodule2.csv
2025-08-07 13:55:22,083 - core.model_trainer - INFO - 准备训练数据...
2025-08-07 13:55:22,085 - core.model_trainer - INFO - 数据准备完成 - 训练集: 157, 测试集: 40
2025-08-07 13:55:22,085 - core.model_trainer - INFO - 开始训练 RandomForest 模型...
2025-08-07 13:55:22,247 - core.model_trainer - INFO - RandomForest 训练完成 - 用时: 0.12秒
2025-08-07 13:55:22,248 - core.model_trainer - INFO - 训练完成！成功训练 1 个模型
2025-08-07 13:56:00,114 - core.model_trainer - INFO - 准备训练数据...
2025-08-07 13:56:00,118 - core.model_trainer - INFO - 数据准备完成 - 训练集: 157, 测试集: 40
2025-08-07 13:56:00,118 - core.model_trainer - INFO - 开始训练 DecisionTree 模型...
2025-08-07 13:56:00,122 - core.model_trainer - INFO - DecisionTree 训练完成 - 用时: 0.00秒
2025-08-07 13:56:00,123 - core.model_trainer - INFO - 开始训练 RandomForest 模型...
2025-08-07 13:56:00,311 - core.model_trainer - INFO - RandomForest 训练完成 - 用时: 0.12秒
2025-08-07 13:56:00,312 - core.model_trainer - INFO - 开始训练 XGBoost 模型...
2025-08-07 13:56:00,374 - core.model_trainer - INFO - XGBoost 训练完成 - 用时: 0.06秒
2025-08-07 13:56:00,375 - core.model_trainer - INFO - 开始训练 LightGBM 模型...
2025-08-07 13:56:01,911 - core.model_trainer - INFO - LightGBM 训练完成 - 用时: 1.54秒
2025-08-07 13:56:01,911 - core.model_trainer - INFO - 开始训练 CatBoost 模型...
2025-08-07 13:56:02,194 - core.model_trainer - INFO - CatBoost 训练完成 - 用时: 0.28秒
2025-08-07 13:56:02,195 - core.model_trainer - INFO - 开始训练 Logistic 模型...
2025-08-07 13:56:02,290 - core.model_trainer - INFO - Logistic 训练完成 - 用时: 0.09秒
2025-08-07 13:56:02,291 - core.model_trainer - INFO - 开始训练 SVM 模型...
2025-08-07 13:56:02,300 - core.model_trainer - INFO - SVM 训练完成 - 用时: 0.00秒
2025-08-07 13:56:02,302 - core.model_trainer - INFO - 开始训练 KNN 模型...
2025-08-07 13:56:02,304 - core.model_trainer - INFO - KNN 训练完成 - 用时: 0.00秒
2025-08-07 13:56:02,312 - core.model_trainer - INFO - 开始训练 NaiveBayes 模型...
2025-08-07 13:56:02,312 - core.model_trainer - ERROR - 训练 NaiveBayes 失败: 朴素贝叶斯仅支持分类任务
2025-08-07 13:56:02,321 - core.model_trainer - INFO - 开始训练 NeuralNet 模型...
2025-08-07 13:56:02,402 - core.model_trainer - INFO - NeuralNet 训练完成 - 用时: 0.08秒
2025-08-07 13:56:02,402 - core.model_trainer - INFO - 训练完成！成功训练 10 个模型
2025-08-07 13:56:51,647 - __main__ - INFO - 应用程序正在退出
2025-08-07 13:56:51,649 - gui.core.config_manager - INFO - 配置已保存到文件: D:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 13:56:54,240 - __main__ - INFO - 应用程序正在退出
2025-08-07 13:56:54,247 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 13:57:05,536 - __main__ - INFO - 项目路径: d:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-07 13:57:05,536 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 13:57:06,124 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-07 13:57:06,124 - __main__ - INFO - 启动GUI主循环
2025-08-07 13:57:17,788 - ProjectHistoryTab - INFO - 添加历史记录: 数据加载: C:/Users/<USER>/Desktop/parameters/0807/nodule2.csv
2025-08-07 13:57:19,235 - ModelTrainerTab - INFO - 接收到数据: (197, 9)
2025-08-07 13:57:19,235 - DataVisualizationTab - INFO - 数值列: 9, 分类列: 0
2025-08-07 13:57:19,249 - DataVisualizationTab - INFO - 接收到数据: (197, 9)
2025-08-07 13:57:19,249 - ProjectHistoryTab - INFO - 添加历史记录: 数据加载: C:/Users/<USER>/Desktop/parameters/0807/nodule2.csv
2025-08-07 13:57:37,442 - core.model_trainer - INFO - 准备训练数据...
2025-08-07 13:57:37,443 - core.model_trainer - INFO - 数据准备完成 - 训练集: 157, 测试集: 40
2025-08-07 13:57:37,444 - core.model_trainer - INFO - 开始训练 DecisionTree 模型...
2025-08-07 13:57:37,446 - core.model_trainer - INFO - DecisionTree 训练完成 - 用时: 0.00秒
2025-08-07 13:57:37,447 - core.model_trainer - INFO - 开始训练 RandomForest 模型...
2025-08-07 13:57:37,610 - core.model_trainer - INFO - RandomForest 训练完成 - 用时: 0.11秒
2025-08-07 13:57:37,611 - core.model_trainer - INFO - 开始训练 XGBoost 模型...
2025-08-07 13:57:37,660 - core.model_trainer - INFO - XGBoost 训练完成 - 用时: 0.05秒
2025-08-07 13:57:37,661 - core.model_trainer - INFO - 开始训练 LightGBM 模型...
2025-08-07 13:57:39,041 - core.model_trainer - INFO - LightGBM 训练完成 - 用时: 1.37秒
2025-08-07 13:57:39,043 - core.model_trainer - INFO - 开始训练 CatBoost 模型...
2025-08-07 13:57:39,308 - core.model_trainer - INFO - CatBoost 训练完成 - 用时: 0.26秒
2025-08-07 13:57:39,308 - core.model_trainer - INFO - 开始训练 Logistic 模型...
2025-08-07 13:57:39,726 - core.model_trainer - INFO - Logistic 训练完成 - 用时: 0.41秒
2025-08-07 13:57:39,727 - core.model_trainer - INFO - 开始训练 SVM 模型...
2025-08-07 13:57:39,737 - core.model_trainer - INFO - SVM 训练完成 - 用时: 0.00秒
2025-08-07 13:57:39,738 - core.model_trainer - INFO - 开始训练 KNN 模型...
2025-08-07 13:57:39,748 - core.model_trainer - INFO - KNN 训练完成 - 用时: 0.00秒
2025-08-07 13:57:39,750 - core.model_trainer - INFO - 开始训练 NaiveBayes 模型...
2025-08-07 13:57:39,750 - core.model_trainer - ERROR - 训练 NaiveBayes 失败: 朴素贝叶斯仅支持分类任务
2025-08-07 13:57:39,758 - core.model_trainer - INFO - 开始训练 NeuralNet 模型...
2025-08-07 13:57:39,839 - core.model_trainer - INFO - NeuralNet 训练完成 - 用时: 0.08秒
2025-08-07 13:57:39,840 - core.model_trainer - INFO - 训练完成！成功训练 10 个模型
2025-08-07 14:00:46,103 - __main__ - INFO - 应用程序正在退出
2025-08-07 14:00:46,105 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 14:04:05,425 - __main__ - INFO - 项目路径: D:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-07 14:04:05,426 - gui.core.config_manager - INFO - 配置已从文件加载: D:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 14:04:05,982 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-07 14:04:05,982 - __main__ - INFO - 启动GUI主循环
2025-08-07 14:04:34,719 - __main__ - INFO - 应用程序正在退出
2025-08-07 14:04:34,720 - gui.core.config_manager - INFO - 配置已保存到文件: D:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 14:04:43,299 - __main__ - INFO - 项目路径: d:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-07 14:04:43,301 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 14:04:43,879 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-07 14:04:43,880 - __main__ - INFO - 启动GUI主循环
2025-08-07 14:05:01,758 - ProjectHistoryTab - INFO - 添加历史记录: 数据加载: C:/Users/<USER>/Desktop/parameters/0807/nodule3.csv
2025-08-07 14:05:03,184 - ModelTrainerTab - INFO - 接收到数据: (197, 9)
2025-08-07 14:05:03,185 - DataVisualizationTab - INFO - 数值列: 9, 分类列: 0
2025-08-07 14:05:03,198 - DataVisualizationTab - INFO - 接收到数据: (197, 9)
2025-08-07 14:05:03,198 - ProjectHistoryTab - INFO - 添加历史记录: 数据加载: C:/Users/<USER>/Desktop/parameters/0807/nodule3.csv
2025-08-07 14:05:17,200 - ModelTrainerTab - INFO - 接收到预处理后的数据
2025-08-07 14:05:17,205 - DataManagementModule - INFO - 数据分割完成: 训练集 157 样本, 测试集 40 样本
2025-08-07 14:05:28,545 - core.model_trainer - INFO - 准备训练数据...
2025-08-07 14:05:28,546 - core.model_trainer - INFO - 数据准备完成 - 训练集: 157, 测试集: 40
2025-08-07 14:05:28,546 - core.model_trainer - INFO - 开始训练 DecisionTree 模型...
2025-08-07 14:05:28,550 - core.model_trainer - INFO - DecisionTree 训练完成 - 用时: 0.00秒
2025-08-07 14:05:28,551 - core.model_trainer - INFO - 开始训练 RandomForest 模型...
2025-08-07 14:05:28,729 - core.model_trainer - INFO - RandomForest 训练完成 - 用时: 0.11秒
2025-08-07 14:05:28,729 - core.model_trainer - INFO - 开始训练 XGBoost 模型...
2025-08-07 14:05:28,787 - core.model_trainer - INFO - XGBoost 训练完成 - 用时: 0.05秒
2025-08-07 14:05:28,788 - core.model_trainer - INFO - 开始训练 LightGBM 模型...
2025-08-07 14:05:30,158 - core.model_trainer - INFO - LightGBM 训练完成 - 用时: 1.36秒
2025-08-07 14:05:30,160 - core.model_trainer - INFO - 开始训练 CatBoost 模型...
2025-08-07 14:05:30,409 - core.model_trainer - INFO - CatBoost 训练完成 - 用时: 0.24秒
2025-08-07 14:05:30,410 - core.model_trainer - INFO - 开始训练 Logistic 模型...
2025-08-07 14:05:30,845 - core.model_trainer - INFO - Logistic 训练完成 - 用时: 0.43秒
2025-08-07 14:05:30,849 - core.model_trainer - INFO - 开始训练 SVM 模型...
2025-08-07 14:05:30,852 - core.model_trainer - INFO - SVM 训练完成 - 用时: 0.00秒
2025-08-07 14:05:30,858 - core.model_trainer - INFO - 开始训练 KNN 模型...
2025-08-07 14:05:30,861 - core.model_trainer - INFO - KNN 训练完成 - 用时: 0.00秒
2025-08-07 14:05:30,870 - core.model_trainer - INFO - 开始训练 NaiveBayes 模型...
2025-08-07 14:05:30,877 - core.model_trainer - ERROR - 训练 NaiveBayes 失败: 朴素贝叶斯仅支持分类任务
2025-08-07 14:05:30,879 - core.model_trainer - INFO - 开始训练 NeuralNet 模型...
2025-08-07 14:05:30,961 - core.model_trainer - INFO - NeuralNet 训练完成 - 用时: 0.08秒
2025-08-07 14:05:30,961 - core.model_trainer - INFO - 训练完成！成功训练 10 个模型
2025-08-07 14:08:19,150 - __main__ - INFO - 应用程序正在退出
2025-08-07 14:08:19,152 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 14:11:59,746 - __main__ - INFO - 项目路径: D:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-07 14:11:59,746 - gui.core.config_manager - INFO - 配置已从文件加载: D:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 14:12:00,290 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-07 14:12:00,290 - __main__ - INFO - 启动GUI主循环
2025-08-07 14:13:25,784 - ProjectHistoryTab - INFO - 添加历史记录: 数据加载: C:/Users/<USER>/Desktop/parameters/0807/nodule3.csv
2025-08-07 14:13:26,912 - ModelTrainerTab - INFO - 接收到数据: (197, 9)
2025-08-07 14:13:26,912 - DataVisualizationTab - INFO - 数值列: 9, 分类列: 0
2025-08-07 14:13:26,924 - DataVisualizationTab - INFO - 接收到数据: (197, 9)
2025-08-07 14:13:26,925 - ProjectHistoryTab - INFO - 添加历史记录: 数据加载: C:/Users/<USER>/Desktop/parameters/0807/nodule3.csv
2025-08-07 14:13:33,195 - ModelTrainerTab - INFO - 接收到预处理后的数据
2025-08-07 14:13:33,200 - DataManagementModule - INFO - 数据分割完成: 训练集 157 样本, 测试集 40 样本
2025-08-07 14:13:38,970 - core.model_trainer - INFO - 准备训练数据...
2025-08-07 14:13:38,970 - core.model_trainer - INFO - 数据准备完成 - 训练集: 157, 测试集: 40
2025-08-07 14:13:38,970 - core.model_trainer - INFO - 开始训练 DecisionTree 模型...
2025-08-07 14:13:38,980 - core.model_trainer - INFO - DecisionTree 训练完成 - 用时: 0.01秒
2025-08-07 14:13:38,981 - core.model_trainer - INFO - 开始训练 RandomForest 模型...
2025-08-07 14:13:39,124 - core.model_trainer - INFO - RandomForest 训练完成 - 用时: 0.11秒
2025-08-07 14:13:39,125 - core.model_trainer - INFO - 开始训练 XGBoost 模型...
2025-08-07 14:13:39,250 - core.model_trainer - INFO - XGBoost 训练完成 - 用时: 0.12秒
2025-08-07 14:13:39,251 - core.model_trainer - INFO - 开始训练 LightGBM 模型...
2025-08-07 14:13:40,681 - core.model_trainer - INFO - LightGBM 训练完成 - 用时: 1.43秒
2025-08-07 14:13:40,683 - core.model_trainer - INFO - 开始训练 CatBoost 模型...
2025-08-07 14:13:40,921 - core.model_trainer - INFO - CatBoost 训练完成 - 用时: 0.24秒
2025-08-07 14:13:40,925 - core.model_trainer - INFO - 开始训练 Logistic 模型...
2025-08-07 14:13:40,926 - core.model_trainer - INFO - Logistic 训练完成 - 用时: 0.00秒
2025-08-07 14:13:40,932 - core.model_trainer - INFO - 开始训练 SVM 模型...
2025-08-07 14:13:40,935 - core.model_trainer - INFO - SVM 训练完成 - 用时: 0.00秒
2025-08-07 14:13:40,938 - core.model_trainer - INFO - 开始训练 KNN 模型...
2025-08-07 14:13:40,942 - core.model_trainer - INFO - KNN 训练完成 - 用时: 0.00秒
2025-08-07 14:13:40,942 - core.model_trainer - INFO - 开始训练 NaiveBayes 模型...
2025-08-07 14:13:40,942 - core.model_trainer - ERROR - 训练 NaiveBayes 失败: 朴素贝叶斯仅支持分类任务
2025-08-07 14:13:40,943 - core.model_trainer - INFO - 开始训练 NeuralNet 模型...
2025-08-07 14:13:41,048 - core.model_trainer - INFO - NeuralNet 训练完成 - 用时: 0.10秒
2025-08-07 14:13:41,049 - core.model_trainer - INFO - 训练完成！成功训练 10 个模型
2025-08-07 14:13:49,476 - __main__ - INFO - 应用程序正在退出
2025-08-07 14:13:49,479 - gui.core.config_manager - INFO - 配置已保存到文件: D:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 14:14:45,475 - __main__ - INFO - 项目路径: d:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-07 14:14:45,475 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 14:14:46,057 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-07 14:14:46,057 - __main__ - INFO - 启动GUI主循环
2025-08-07 14:14:59,551 - ProjectHistoryTab - INFO - 添加历史记录: 数据加载: C:/Users/<USER>/Desktop/parameters/0807/nodule3.csv
2025-08-07 14:15:00,635 - ModelTrainerTab - INFO - 接收到数据: (197, 9)
2025-08-07 14:15:00,635 - DataVisualizationTab - INFO - 数值列: 9, 分类列: 0
2025-08-07 14:15:00,650 - DataVisualizationTab - INFO - 接收到数据: (197, 9)
2025-08-07 14:15:00,651 - ProjectHistoryTab - INFO - 添加历史记录: 数据加载: C:/Users/<USER>/Desktop/parameters/0807/nodule3.csv
2025-08-07 14:15:08,675 - ModelTrainerTab - INFO - 接收到预处理后的数据
2025-08-07 14:15:08,678 - DataManagementModule - INFO - 数据分割完成: 训练集 157 样本, 测试集 40 样本
2025-08-07 14:15:21,951 - core.model_trainer - INFO - 准备训练数据...
2025-08-07 14:15:21,953 - core.model_trainer - INFO - 数据准备完成 - 训练集: 157, 测试集: 40
2025-08-07 14:15:21,953 - core.model_trainer - INFO - 开始训练 DecisionTree 模型...
2025-08-07 14:15:21,957 - core.model_trainer - INFO - DecisionTree 训练完成 - 用时: 0.00秒
2025-08-07 14:15:21,957 - core.model_trainer - INFO - 开始训练 RandomForest 模型...
2025-08-07 14:15:22,146 - core.model_trainer - INFO - RandomForest 训练完成 - 用时: 0.12秒
2025-08-07 14:15:22,146 - core.model_trainer - INFO - 开始训练 XGBoost 模型...
2025-08-07 14:15:22,198 - core.model_trainer - INFO - XGBoost 训练完成 - 用时: 0.05秒
2025-08-07 14:15:22,199 - core.model_trainer - INFO - 开始训练 LightGBM 模型...
2025-08-07 14:15:23,583 - core.model_trainer - INFO - LightGBM 训练完成 - 用时: 1.38秒
2025-08-07 14:15:23,585 - core.model_trainer - INFO - 开始训练 CatBoost 模型...
2025-08-07 14:15:23,791 - core.model_trainer - INFO - CatBoost 训练完成 - 用时: 0.21秒
2025-08-07 14:15:23,791 - core.model_trainer - INFO - 开始训练 Logistic 模型...
2025-08-07 14:15:23,961 - core.model_trainer - INFO - Logistic 训练完成 - 用时: 0.16秒
2025-08-07 14:15:23,961 - core.model_trainer - INFO - 开始训练 SVM 模型...
2025-08-07 14:15:23,966 - core.model_trainer - INFO - SVM 训练完成 - 用时: 0.00秒
2025-08-07 14:15:23,971 - core.model_trainer - INFO - 开始训练 KNN 模型...
2025-08-07 14:15:23,980 - core.model_trainer - INFO - KNN 训练完成 - 用时: 0.00秒
2025-08-07 14:15:23,982 - core.model_trainer - INFO - 开始训练 NaiveBayes 模型...
2025-08-07 14:15:23,983 - core.model_trainer - ERROR - 训练 NaiveBayes 失败: 朴素贝叶斯仅支持分类任务
2025-08-07 14:15:23,983 - core.model_trainer - INFO - 开始训练 NeuralNet 模型...
2025-08-07 14:15:24,061 - core.model_trainer - INFO - NeuralNet 训练完成 - 用时: 0.08秒
2025-08-07 14:15:24,061 - core.model_trainer - INFO - 训练完成！成功训练 10 个模型
2025-08-07 14:16:01,330 - __main__ - INFO - 应用程序正在退出
2025-08-07 14:16:01,330 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 14:18:26,216 - __main__ - INFO - 项目路径: d:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-07 14:18:26,217 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 14:18:26,821 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-07 14:18:26,821 - __main__ - INFO - 启动GUI主循环
2025-08-07 14:18:39,740 - ProjectHistoryTab - INFO - 添加历史记录: 数据加载: C:/Users/<USER>/Desktop/parameters/0807/nodule2.csv
2025-08-07 14:18:40,983 - ModelTrainerTab - INFO - 接收到数据: (197, 9)
2025-08-07 14:18:40,983 - DataVisualizationTab - INFO - 数值列: 9, 分类列: 0
2025-08-07 14:18:40,996 - DataVisualizationTab - INFO - 接收到数据: (197, 9)
2025-08-07 14:18:40,996 - ProjectHistoryTab - INFO - 添加历史记录: 数据加载: C:/Users/<USER>/Desktop/parameters/0807/nodule2.csv
2025-08-07 14:18:46,707 - ModelTrainerTab - INFO - 接收到预处理后的数据
2025-08-07 14:18:46,711 - DataManagementModule - INFO - 数据分割完成: 训练集 157 样本, 测试集 40 样本
2025-08-07 14:18:51,256 - core.model_trainer - INFO - 准备训练数据...
2025-08-07 14:18:51,257 - core.model_trainer - INFO - 数据准备完成 - 训练集: 157, 测试集: 40
2025-08-07 14:18:51,257 - core.model_trainer - INFO - 开始训练 DecisionTree 模型...
2025-08-07 14:18:51,257 - core.model_trainer - INFO - DecisionTree 训练完成 - 用时: 0.00秒
2025-08-07 14:18:51,261 - core.model_trainer - INFO - 开始训练 RandomForest 模型...
2025-08-07 14:18:51,419 - core.model_trainer - INFO - RandomForest 训练完成 - 用时: 0.10秒
2025-08-07 14:18:51,419 - core.model_trainer - INFO - 开始训练 XGBoost 模型...
2025-08-07 14:18:51,475 - core.model_trainer - INFO - XGBoost 训练完成 - 用时: 0.06秒
2025-08-07 14:18:51,476 - core.model_trainer - INFO - 开始训练 LightGBM 模型...
2025-08-07 14:18:52,852 - core.model_trainer - INFO - LightGBM 训练完成 - 用时: 1.38秒
2025-08-07 14:18:52,852 - core.model_trainer - INFO - 开始训练 CatBoost 模型...
2025-08-07 14:18:53,040 - core.model_trainer - INFO - CatBoost 训练完成 - 用时: 0.18秒
2025-08-07 14:18:53,040 - core.model_trainer - INFO - 开始训练 Logistic 模型...
2025-08-07 14:18:53,178 - core.model_trainer - INFO - Logistic 训练完成 - 用时: 0.14秒
2025-08-07 14:18:53,185 - core.model_trainer - INFO - 开始训练 SVM 模型...
2025-08-07 14:18:53,196 - core.model_trainer - INFO - SVM 训练完成 - 用时: 0.00秒
2025-08-07 14:18:53,197 - core.model_trainer - INFO - 开始训练 KNN 模型...
2025-08-07 14:18:53,199 - core.model_trainer - INFO - KNN 训练完成 - 用时: 0.00秒
2025-08-07 14:18:53,208 - core.model_trainer - INFO - 开始训练 NaiveBayes 模型...
2025-08-07 14:18:53,208 - core.model_trainer - ERROR - 训练 NaiveBayes 失败: 朴素贝叶斯仅支持分类任务
2025-08-07 14:18:53,216 - core.model_trainer - INFO - 开始训练 NeuralNet 模型...
2025-08-07 14:18:53,296 - core.model_trainer - INFO - NeuralNet 训练完成 - 用时: 0.08秒
2025-08-07 14:18:53,296 - core.model_trainer - INFO - 训练完成！成功训练 10 个模型
2025-08-07 14:19:37,707 - __main__ - INFO - 应用程序正在退出
2025-08-07 14:19:37,707 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 14:25:34,174 - __main__ - INFO - 项目路径: d:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-07 14:25:34,175 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 14:25:34,770 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-07 14:25:34,770 - __main__ - INFO - 启动GUI主循环
2025-08-07 14:26:10,774 - __main__ - INFO - 应用程序正在退出
2025-08-07 14:26:10,781 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 14:26:16,002 - __main__ - INFO - 项目路径: d:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-07 14:26:16,003 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 14:26:16,558 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-07 14:26:16,560 - __main__ - INFO - 启动GUI主循环
2025-08-07 14:26:27,944 - ProjectHistoryTab - INFO - 添加历史记录: 数据加载: C:/Users/<USER>/Desktop/parameters/0807/nodule2.csv
2025-08-07 14:26:29,181 - ModelTrainerTab - INFO - 接收到数据: (197, 9)
2025-08-07 14:26:29,181 - DataVisualizationTab - INFO - 数值列: 9, 分类列: 0
2025-08-07 14:26:29,194 - DataVisualizationTab - INFO - 接收到数据: (197, 9)
2025-08-07 14:26:29,195 - ProjectHistoryTab - INFO - 添加历史记录: 数据加载: C:/Users/<USER>/Desktop/parameters/0807/nodule2.csv
2025-08-07 14:26:38,098 - ModelTrainerTab - INFO - 接收到预处理后的数据
2025-08-07 14:26:38,103 - DataManagementModule - INFO - 数据分割完成: 训练集 157 样本, 测试集 40 样本
2025-08-07 14:26:43,390 - ModelTrainerTab - INFO - 检测到任务类型: regression
2025-08-07 14:26:43,391 - core.model_trainer - INFO - 准备训练数据...
2025-08-07 14:26:43,391 - core.model_trainer - INFO - 数据准备完成 - 训练集: 157, 测试集: 40
2025-08-07 14:26:43,391 - core.model_trainer - INFO - 开始训练 DecisionTree 模型...
2025-08-07 14:26:43,395 - core.model_trainer - INFO - DecisionTree 训练完成 - 用时: 0.00秒
2025-08-07 14:26:43,395 - core.model_trainer - INFO - 开始训练 RandomForest 模型...
2025-08-07 14:26:43,540 - core.model_trainer - INFO - RandomForest 训练完成 - 用时: 0.10秒
2025-08-07 14:26:43,540 - core.model_trainer - INFO - 开始训练 XGBoost 模型...
2025-08-07 14:26:43,593 - core.model_trainer - INFO - XGBoost 训练完成 - 用时: 0.05秒
2025-08-07 14:26:43,593 - core.model_trainer - INFO - 开始训练 LightGBM 模型...
2025-08-07 14:26:44,958 - core.model_trainer - INFO - LightGBM 训练完成 - 用时: 1.36秒
2025-08-07 14:26:44,960 - core.model_trainer - INFO - 开始训练 CatBoost 模型...
2025-08-07 14:26:45,155 - core.model_trainer - INFO - CatBoost 训练完成 - 用时: 0.19秒
2025-08-07 14:26:45,158 - core.model_trainer - INFO - 开始训练 Logistic 模型...
2025-08-07 14:26:45,665 - core.model_trainer - INFO - Logistic 训练完成 - 用时: 0.50秒
2025-08-07 14:26:45,665 - core.model_trainer - INFO - 开始训练 SVM 模型...
2025-08-07 14:26:45,667 - core.model_trainer - INFO - SVM 训练完成 - 用时: 0.00秒
2025-08-07 14:26:45,667 - core.model_trainer - INFO - 开始训练 KNN 模型...
2025-08-07 14:26:45,682 - core.model_trainer - INFO - KNN 训练完成 - 用时: 0.00秒
2025-08-07 14:26:45,685 - core.model_trainer - INFO - 开始训练 NaiveBayes 模型...
2025-08-07 14:26:45,692 - core.model_trainer - ERROR - 训练 NaiveBayes 失败: 朴素贝叶斯仅支持分类任务
2025-08-07 14:26:45,695 - core.model_trainer - INFO - 开始训练 NeuralNet 模型...
2025-08-07 14:26:45,768 - core.model_trainer - INFO - NeuralNet 训练完成 - 用时: 0.06秒
2025-08-07 14:26:45,783 - core.model_trainer - INFO - 训练完成！成功训练 10 个模型
2025-08-07 14:26:56,376 - __main__ - INFO - 应用程序正在退出
2025-08-07 14:26:56,389 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 21:56:05,056 - __main__ - INFO - 项目路径: D:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-07 21:56:05,057 - gui.core.config_manager - INFO - 配置已从文件加载: D:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 21:59:36,641 - __main__ - INFO - 项目路径: D:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-07 21:59:36,642 - gui.core.config_manager - INFO - 配置已从文件加载: D:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 21:59:37,244 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-07 21:59:37,244 - __main__ - INFO - 启动GUI主循环
2025-08-07 22:08:18,422 - __main__ - INFO - 应用程序正在退出
2025-08-07 22:08:18,424 - gui.core.config_manager - INFO - 配置已保存到文件: D:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 23:13:29,806 - __main__ - INFO - 项目路径: D:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-07 23:13:29,806 - gui.core.config_manager - INFO - 配置已从文件加载: D:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 23:28:52,043 - __main__ - INFO - 项目路径: D:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-07 23:28:52,043 - gui.core.config_manager - INFO - 配置已从文件加载: D:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 23:28:52,375 - refactored_ml_platform.gui.core.config_manager - INFO - 配置已从文件加载: D:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 23:28:52,638 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-07 23:28:52,638 - __main__ - INFO - 启动GUI主循环
2025-08-07 23:29:29,792 - __main__ - INFO - 应用程序正在退出
2025-08-07 23:29:29,793 - gui.core.config_manager - INFO - 配置已保存到文件: D:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 23:32:29,723 - __main__ - INFO - 项目路径: d:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-07 23:32:29,723 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 23:32:40,207 - __main__ - INFO - 项目路径: d:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-07 23:32:40,208 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 23:34:17,670 - __main__ - INFO - 项目路径: d:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-07 23:34:17,670 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-07 23:34:18,098 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-07 23:34:18,099 - __main__ - INFO - 启动GUI主循环
2025-08-07 23:34:45,572 - __main__ - INFO - 应用程序正在退出
2025-08-07 23:34:45,573 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-13 09:11:10,321 - __main__ - INFO - 项目路径: d:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-13 09:11:10,321 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-13 09:11:10,761 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-13 09:11:10,761 - __main__ - INFO - 启动GUI主循环
2025-08-13 09:12:05,064 - __main__ - INFO - 应用程序正在退出
2025-08-13 09:12:05,064 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-13 09:40:03,958 - __main__ - INFO - 项目路径: d:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-13 09:40:03,960 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-13 09:40:04,392 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-13 09:40:04,392 - __main__ - INFO - 启动GUI主循环
2025-08-13 09:40:27,772 - __main__ - INFO - 应用程序正在退出
2025-08-13 09:40:27,774 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-13 09:43:02,622 - __main__ - INFO - 项目路径: D:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-13 09:43:02,622 - gui.core.config_manager - INFO - 配置已从文件加载: D:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-13 09:43:03,095 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-13 09:43:03,095 - __main__ - INFO - 启动GUI主循环
2025-08-13 09:43:14,228 - __main__ - INFO - 应用程序正在退出
2025-08-13 09:43:14,228 - gui.core.config_manager - INFO - 配置已保存到文件: D:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-13 10:23:34,526 - __main__ - INFO - 项目路径: d:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-13 10:23:34,526 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-13 10:23:34,972 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-13 10:23:34,972 - __main__ - INFO - 启动GUI主循环
2025-08-13 10:23:54,214 - __main__ - INFO - 应用程序正在退出
2025-08-13 10:23:54,215 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-23 11:48:34,582 - __main__ - INFO - 项目路径: d:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-23 11:48:34,584 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-23 11:48:35,062 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-23 11:48:35,062 - __main__ - INFO - 启动GUI主循环
2025-08-23 11:49:25,359 - __main__ - INFO - 应用程序正在退出
2025-08-23 11:49:25,359 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-23 12:02:43,377 - __main__ - INFO - 项目路径: d:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-23 12:02:43,377 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-23 12:02:43,862 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-23 12:02:43,862 - __main__ - INFO - 启动GUI主循环
2025-08-23 12:03:29,565 - ModelTrainingManager - INFO - 模型训练模块已接收数据预处理完成事件
2025-08-23 12:03:37,960 - __main__ - INFO - 应用程序正在退出
2025-08-23 12:03:37,961 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-23 12:10:17,931 - __main__ - INFO - 项目路径: d:\Code\multi_model_01_updated\refactored_ml_platform
2025-08-23 12:10:17,931 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-23 12:10:18,411 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-23 12:10:18,411 - __main__ - INFO - 启动GUI主循环
2025-08-23 12:10:24,359 - __main__ - INFO - 应用程序正在退出
2025-08-23 12:10:24,361 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\multi_model_01_updated\refactored_ml_platform\config\gui_config.json
2025-08-23 12:24:36,653 - __main__ - INFO - 项目路径: D:\Code\MM01U\refactored_ml_platform
2025-08-23 12:24:36,658 - gui.core.config_manager - INFO - 配置已从文件加载: D:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 12:24:37,142 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-23 12:24:37,142 - __main__ - INFO - 启动GUI主循环
2025-08-23 12:33:41,360 - __main__ - INFO - 应用程序正在退出
2025-08-23 12:33:41,362 - gui.core.config_manager - INFO - 配置已保存到文件: D:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 12:41:12,060 - __main__ - INFO - 项目路径: d:\Code\MM01U\refactored_ml_platform
2025-08-23 12:41:12,060 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 12:41:12,560 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-23 12:41:12,560 - __main__ - INFO - 启动GUI主循环
2025-08-23 12:41:32,473 - __main__ - INFO - 应用程序正在退出
2025-08-23 12:41:32,474 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 12:47:37,409 - __main__ - INFO - 项目路径: d:\Code\MM01U\refactored_ml_platform
2025-08-23 12:47:37,409 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 12:47:37,895 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-23 12:47:37,895 - __main__ - INFO - 启动GUI主循环
2025-08-23 12:47:55,582 - ModelTrainingManager - INFO - 模型训练模块已接收数据预处理完成事件
2025-08-23 12:48:12,089 - __main__ - INFO - 应用程序正在退出
2025-08-23 12:48:12,090 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 12:57:13,534 - __main__ - INFO - 项目路径: d:\Code\MM01U\refactored_ml_platform
2025-08-23 12:57:13,535 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 12:57:14,026 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-23 12:57:14,026 - __main__ - INFO - 启动GUI主循环
2025-08-23 12:57:31,328 - ModelTrainingManager - INFO - 模型训练模块已接收数据预处理完成事件
2025-08-23 12:57:47,122 - __main__ - INFO - 应用程序正在退出
2025-08-23 12:57:47,123 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 13:06:02,893 - __main__ - INFO - 项目路径: d:\Code\MM01U\refactored_ml_platform
2025-08-23 13:06:02,893 - gui.core.config_manager - INFO - 配置已从文件加载: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 13:06:03,374 - __main__ - INFO - 重构版机器学习平台初始化完成
2025-08-23 13:06:03,374 - __main__ - INFO - 启动GUI主循环
2025-08-23 13:06:12,251 - ModelTrainingManager - WARNING - 数据加载事件中缺少'data'字段
2025-08-23 13:06:13,219 - ModelTrainingManager - INFO - 模型训练模块已加载数据: (197, 9)
2025-08-23 13:06:16,215 - ModelTrainingManager - INFO - 模型训练模块已接收预处理数据: 训练集157行, 测试集40行
2025-08-23 13:06:43,578 - __main__ - INFO - 应用程序正在退出
2025-08-23 13:06:43,579 - gui.core.config_manager - INFO - 配置已保存到文件: d:\Code\MM01U\refactored_ml_platform\config\gui_config.json
2025-08-23 14:39:17,811 - __main__ - INFO - 项目路径: d:\Code\MM01U\refactored_ml_platform
2025-08-23 14:39:19,173 - __main__ - ERROR - GUI模块导入失败: attempted relative import beyond top-level package
2025-08-23 14:39:24,928 - __main__ - INFO - 项目路径: d:\Code\MM01U\refactored_ml_platform
2025-08-23 14:39:26,230 - __main__ - ERROR - GUI模块导入失败: attempted relative import beyond top-level package
2025-08-23 15:13:08,046 - __main__ - INFO - 项目路径: d:\Code\MM01U\refactored_ml_platform
2025-08-23 15:13:09,624 - __main__ - ERROR - GUI模块导入失败: No module named 'core.event_manager'
2025-08-23 15:13:33,884 - __main__ - INFO - 项目路径: d:\Code\MM01U\refactored_ml_platform
2025-08-23 15:13:35,291 - __main__ - ERROR - GUI模块导入失败: No module named 'core.event_manager'
