#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
学术级图表GUI模块
提供符合学术论文发表标准的专业可视化工具
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
import threading
from pathlib import Path
from typing import Dict, List, Optional, Any

from ....utils.academic_plots import get_academic_plot_manager
from ....utils.data_loader import get_data_loader
from ....core.event_manager import get_event_manager
from ....utils.error_handler import get_error_handler


class AcademicPlotsGUI:
    """学术级图表GUI"""
    
    def __init__(self, parent):
        """初始化学术级图表GUI"""
        self.parent = parent
        self.academic_plot_manager = get_academic_plot_manager()
        self.data_loader = get_data_loader()
        self.event_manager = get_event_manager()
        self.error_handler = get_error_handler()
        
        # 数据存储
        self.current_data = None
        self.current_figure = None
        
        # GUI组件
        self.frame = None
        
        # 控制变量
        self.plot_type_var = tk.StringVar(value="相关性矩阵")
        self.journal_style_var = tk.StringVar(value="nature")
        self.data_path_var = tk.StringVar()
        self.output_path_var = tk.StringVar()
        self.status_var = tk.StringVar(value="就绪")
        
        # 创建界面
        self._create_interface()
    
    def _create_interface(self):
        """创建界面"""
        # 主框架
        self.frame = ttk.Frame(self.parent)
        self.frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 标题
        title_frame = ttk.Frame(self.frame)
        title_frame.pack(fill=tk.X, pady=(0, 20))
        
        ttk.Label(title_frame, text="学术级图表工具", font=('Arial', 16, 'bold')).pack()
        ttk.Label(title_frame, text="创建符合学术论文发表标准的专业图表", 
                 foreground="gray").pack()
        
        # 创建笔记本控件
        self.notebook = ttk.Notebook(self.frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 创建选项卡
        self._create_correlation_tab()
        self._create_roc_comparison_tab()
        self._create_custom_plots_tab()
    
    def _create_correlation_tab(self):
        """创建相关性分析选项卡"""
        corr_tab = ttk.Frame(self.notebook)
        self.notebook.add(corr_tab, text="📊 相关性矩阵")
        
        # 数据输入区域
        data_frame = ttk.LabelFrame(corr_tab, text="数据输入")
        data_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # 文件选择
        file_frame = ttk.Frame(data_frame)
        file_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(file_frame, text="数据文件:").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Entry(file_frame, textvariable=self.data_path_var, width=40).pack(
            side=tk.LEFT, padx=(0, 10), fill=tk.X, expand=True)
        ttk.Button(file_frame, text="浏览...", command=self._browse_data_file).pack(side=tk.RIGHT)
        
        # 参数设置
        params_frame = ttk.LabelFrame(corr_tab, text="参数设置")
        params_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # 第一行参数
        row1 = ttk.Frame(params_frame)
        row1.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(row1, text="相关性方法:").pack(side=tk.LEFT, padx=(0, 10))
        self.corr_method_var = tk.StringVar(value="pearson")
        corr_methods = ["pearson", "spearman", "kendall"]
        self.corr_method_combo = ttk.Combobox(row1, textvariable=self.corr_method_var,
                                             values=corr_methods, state="readonly", width=12)
        self.corr_method_combo.pack(side=tk.LEFT, padx=(0, 20))
        
        ttk.Label(row1, text="期刊风格:").pack(side=tk.LEFT, padx=(0, 10))
        journal_styles = ["nature", "science", "cell", "nejm", "lancet"]
        self.journal_combo = ttk.Combobox(row1, textvariable=self.journal_style_var,
                                         values=journal_styles, state="readonly", width=10)
        self.journal_combo.pack(side=tk.LEFT)
        
        # 第二行参数
        row2 = ttk.Frame(params_frame)
        row2.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        self.show_values_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(row2, text="显示数值", variable=self.show_values_var).pack(side=tk.LEFT, padx=(0, 20))
        
        self.mask_upper_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(row2, text="遮罩上三角", variable=self.mask_upper_var).pack(side=tk.LEFT, padx=(0, 20))
        
        self.significance_test_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(row2, text="显著性检验", variable=self.significance_test_var).pack(side=tk.LEFT)
        
        # 图表显示区域
        chart_frame = ttk.LabelFrame(corr_tab, text="图表预览")
        chart_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建matplotlib图表
        self.corr_fig, self.corr_ax = plt.subplots(figsize=(8, 6))
        self.corr_fig.patch.set_facecolor('white')
        
        # 创建画布
        self.corr_canvas = FigureCanvasTkAgg(self.corr_fig, chart_frame)
        self.corr_canvas.draw()
        self.corr_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 工具栏
        corr_toolbar_frame = ttk.Frame(chart_frame)
        corr_toolbar_frame.pack(fill=tk.X)
        self.corr_toolbar = NavigationToolbar2Tk(self.corr_canvas, corr_toolbar_frame)
        self.corr_toolbar.update()
        
        # 操作按钮
        actions_frame = ttk.Frame(corr_tab)
        actions_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(actions_frame, text="📊 生成图表", 
                  command=self._generate_correlation_plot, style="Accent.TButton").pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(actions_frame, text="💾 保存图表", 
                  command=self._save_correlation_plot).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(actions_frame, text="📋 查看数据", 
                  command=self._preview_data).pack(side=tk.LEFT, padx=(0, 10))
        
        # 初始化空白图表
        self._show_empty_correlation_plot()
    
    def _create_roc_comparison_tab(self):
        """创建ROC比较选项卡"""
        roc_tab = ttk.Frame(self.notebook)
        self.notebook.add(roc_tab, text="📈 ROC比较")
        
        # 说明文本
        info_frame = ttk.Frame(roc_tab)
        info_frame.pack(fill=tk.X, padx=10, pady=10)
        
        info_text = """
此功能用于创建学术级的多模型ROC曲线比较图。
请先在模型训练模块中训练多个模型，然后在此处生成比较图表。
        """
        ttk.Label(info_frame, text=info_text.strip(), justify=tk.LEFT, 
                 foreground="gray").pack(anchor=tk.W)
        
        # ROC参数设置
        roc_params_frame = ttk.LabelFrame(roc_tab, text="ROC图表设置")
        roc_params_frame.pack(fill=tk.X, padx=10, pady=10)
        
        roc_row = ttk.Frame(roc_params_frame)
        roc_row.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(roc_row, text="期刊风格:").pack(side=tk.LEFT, padx=(0, 10))
        self.roc_journal_var = tk.StringVar(value="nature")
        roc_journal_combo = ttk.Combobox(roc_row, textvariable=self.roc_journal_var,
                                        values=["nature", "science", "cell", "nejm", "lancet"],
                                        state="readonly", width=10)
        roc_journal_combo.pack(side=tk.LEFT, padx=(0, 20))
        
        self.show_ci_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(roc_row, text="显示置信区间", variable=self.show_ci_var).pack(side=tk.LEFT)
        
        # ROC图表显示
        roc_chart_frame = ttk.LabelFrame(roc_tab, text="ROC图表预览")
        roc_chart_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建matplotlib图表
        self.roc_fig, self.roc_ax = plt.subplots(figsize=(6, 6))
        self.roc_fig.patch.set_facecolor('white')
        
        # 创建画布
        self.roc_canvas = FigureCanvasTkAgg(self.roc_fig, roc_chart_frame)
        self.roc_canvas.draw()
        self.roc_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 工具栏
        roc_toolbar_frame = ttk.Frame(roc_chart_frame)
        roc_toolbar_frame.pack(fill=tk.X)
        self.roc_toolbar = NavigationToolbar2Tk(self.roc_canvas, roc_toolbar_frame)
        self.roc_toolbar.update()
        
        # ROC操作按钮
        roc_actions_frame = ttk.Frame(roc_tab)
        roc_actions_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(roc_actions_frame, text="📊 生成ROC比较", 
                  command=self._generate_roc_comparison, style="Accent.TButton").pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(roc_actions_frame, text="💾 保存ROC图", 
                  command=self._save_roc_plot).pack(side=tk.LEFT, padx=(0, 10))
        
        # 初始化空白ROC图
        self._show_empty_roc_plot()
    
    def _create_custom_plots_tab(self):
        """创建自定义图表选项卡"""
        custom_tab = ttk.Frame(self.notebook)
        self.notebook.add(custom_tab, text="🎨 自定义图表")
        
        # 图表类型选择
        type_frame = ttk.LabelFrame(custom_tab, text="图表类型")
        type_frame.pack(fill=tk.X, padx=10, pady=10)
        
        type_row = ttk.Frame(type_frame)
        type_row.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(type_row, text="图表类型:").pack(side=tk.LEFT, padx=(0, 10))
        custom_plot_types = ["森林图", "小提琴图", "箱线图", "散点图矩阵"]
        self.custom_plot_type_var = tk.StringVar(value="森林图")
        custom_plot_combo = ttk.Combobox(type_row, textvariable=self.custom_plot_type_var,
                                        values=custom_plot_types, state="readonly", width=15)
        custom_plot_combo.pack(side=tk.LEFT)
        
        # 期刊要求信息
        requirements_frame = ttk.LabelFrame(custom_tab, text="期刊图表要求")
        requirements_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.requirements_text = tk.Text(requirements_frame, height=6, wrap=tk.WORD, 
                                        font=('Consolas', 9), state=tk.DISABLED)
        req_scrollbar = ttk.Scrollbar(requirements_frame, orient=tk.VERTICAL, 
                                     command=self.requirements_text.yview)
        self.requirements_text.configure(yscrollcommand=req_scrollbar.set)
        
        self.requirements_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        req_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
        # 更新期刊要求显示
        self._update_journal_requirements()
        
        # 自定义操作按钮
        custom_actions_frame = ttk.Frame(custom_tab)
        custom_actions_frame.pack(fill=tk.X, padx=10, pady=20)
        
        ttk.Button(custom_actions_frame, text="📋 查看期刊要求", 
                  command=self._update_journal_requirements).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(custom_actions_frame, text="🎨 设置学术样式", 
                  command=self._setup_academic_style).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(custom_actions_frame, text="📏 检查图表规范", 
                  command=self._check_figure_compliance).pack(side=tk.LEFT, padx=(0, 10))
        
        # 状态栏
        status_frame = ttk.Frame(custom_tab)
        status_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        ttk.Label(status_frame, text="状态:").pack(side=tk.LEFT)
        ttk.Label(status_frame, textvariable=self.status_var, foreground="blue").pack(
            side=tk.LEFT, padx=(5, 0))
    
    def _browse_data_file(self):
        """浏览数据文件"""
        filename = filedialog.askopenfilename(
            title="选择数据文件",
            filetypes=[("CSV文件", "*.csv"), ("Excel文件", "*.xlsx"), ("所有文件", "*.*")]
        )
        if filename:
            self.data_path_var.set(filename)
            self._load_data()
    
    def _load_data(self):
        """加载数据"""
        data_path = self.data_path_var.get()
        if not data_path:
            return
        
        try:
            self.current_data = self.data_loader.load_data(data_path)
            self.status_var.set(f"数据已加载 - 形状: {self.current_data.shape}")
            
        except Exception as e:
            self.error_handler.handle_error(e, "加载数据")
            messagebox.showerror("错误", f"数据加载失败: {e}")
    
    def _generate_correlation_plot(self):
        """生成相关性图表"""
        if self.current_data is None:
            messagebox.showwarning("警告", "请先加载数据")
            return
        
        # 在后台线程中生成图表
        threading.Thread(target=self._perform_correlation_plot_generation, daemon=True).start()
    
    def _perform_correlation_plot_generation(self):
        """执行相关性图表生成"""
        try:
            self.status_var.set("正在生成相关性图表...")
            
            # 只选择数值列
            numeric_data = self.current_data.select_dtypes(include=[np.number])
            
            if numeric_data.empty:
                messagebox.showerror("错误", "数据中没有数值列")
                return
            
            # 清空当前图表
            self.corr_ax.clear()
            
            # 生成相关性图表
            fig = self.academic_plot_manager.plot_correlation_matrix(
                numeric_data,
                method=self.corr_method_var.get(),
                figsize=(8, 6),
                show_values=self.show_values_var.get(),
                mask_upper=self.mask_upper_var.get(),
                significance_test=self.significance_test_var.get(),
                journal_style=self.journal_style_var.get()
            )
            
            # 将生成的图表复制到当前画布
            self._copy_plot_to_canvas(fig, self.corr_ax, self.corr_canvas)
            plt.close(fig)
            
            self.current_figure = fig
            self.status_var.set("相关性图表生成完成")
            
        except Exception as e:
            self.error_handler.handle_error(e, "生成相关性图表")
            self.status_var.set("相关性图表生成失败")
            messagebox.showerror("错误", f"图表生成失败: {e}")
    
    def _generate_roc_comparison(self):
        """生成ROC比较图表"""
        try:
            # 这里需要从模型管理器获取模型结果
            # 由于这是一个示例，我们创建一些模拟数据
            messagebox.showinfo("提示", "ROC比较功能需要先训练多个模型。\n请在模型训练模块中训练模型后再使用此功能。")
            
        except Exception as e:
            self.error_handler.handle_error(e, "生成ROC比较图表")
    
    def _copy_plot_to_canvas(self, source_fig, target_ax, target_canvas):
        """将源图表复制到目标画布"""
        try:
            # 这是一个简化的复制方法
            # 实际实现中可能需要更复杂的图表复制逻辑
            target_ax.text(0.5, 0.5, '图表已生成\n请使用保存功能查看完整图表', 
                          ha='center', va='center', transform=target_ax.transAxes,
                          fontsize=12, color='blue')
            target_canvas.draw()
            
        except Exception as e:
            self.logger.warning(f"图表复制失败: {e}")
    
    def _show_empty_correlation_plot(self):
        """显示空白相关性图表"""
        self.corr_ax.clear()
        self.corr_ax.text(0.5, 0.5, '请加载数据并点击"生成图表"', 
                         ha='center', va='center', transform=self.corr_ax.transAxes,
                         fontsize=14, color='gray')
        self.corr_ax.set_xticks([])
        self.corr_ax.set_yticks([])
        self.corr_canvas.draw()
    
    def _show_empty_roc_plot(self):
        """显示空白ROC图表"""
        self.roc_ax.clear()
        self.roc_ax.text(0.5, 0.5, '请先训练多个模型\n然后生成ROC比较图', 
                        ha='center', va='center', transform=self.roc_ax.transAxes,
                        fontsize=14, color='gray')
        self.roc_ax.set_xticks([])
        self.roc_ax.set_yticks([])
        self.roc_canvas.draw()
    
    def _save_correlation_plot(self):
        """保存相关性图表"""
        if self.current_data is None:
            messagebox.showwarning("警告", "请先生成图表")
            return
        
        filename = filedialog.asksaveasfilename(
            title="保存相关性图表",
            defaultextension=".pdf",
            filetypes=[("PDF文件", "*.pdf"), ("PNG文件", "*.png"), ("SVG文件", "*.svg"), ("所有文件", "*.*")]
        )
        
        if filename:
            try:
                # 重新生成高质量图表并保存
                numeric_data = self.current_data.select_dtypes(include=[np.number])
                
                fig = self.academic_plot_manager.plot_correlation_matrix(
                    numeric_data,
                    method=self.corr_method_var.get(),
                    figsize=(10, 8),
                    save_path=filename,
                    show_values=self.show_values_var.get(),
                    mask_upper=self.mask_upper_var.get(),
                    significance_test=self.significance_test_var.get(),
                    journal_style=self.journal_style_var.get()
                )
                
                plt.close(fig)
                messagebox.showinfo("成功", f"相关性图表已保存到: {filename}")
                
            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {e}")
    
    def _save_roc_plot(self):
        """保存ROC图表"""
        messagebox.showinfo("提示", "ROC图表保存功能待实现")
    
    def _preview_data(self):
        """预览数据"""
        if self.current_data is None:
            messagebox.showwarning("警告", "请先加载数据")
            return
        
        # 创建数据预览窗口
        preview_window = tk.Toplevel(self.parent)
        preview_window.title("数据预览")
        preview_window.geometry("800x600")
        
        # 创建表格
        columns = list(self.current_data.columns)
        tree = ttk.Treeview(preview_window, columns=columns, show='headings', height=20)
        
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=100, anchor=tk.CENTER)
        
        # 填充数据（只显示前100行）
        for i, row in self.current_data.head(100).iterrows():
            tree.insert('', 'end', values=list(row))
        
        # 滚动条
        scrollbar_v = ttk.Scrollbar(preview_window, orient=tk.VERTICAL, command=tree.yview)
        scrollbar_h = ttk.Scrollbar(preview_window, orient=tk.HORIZONTAL, command=tree.xview)
        tree.configure(yscrollcommand=scrollbar_v.set, xscrollcommand=scrollbar_h.set)
        
        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        scrollbar_v.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        scrollbar_h.pack(side=tk.BOTTOM, fill=tk.X, padx=5)
    
    def _update_journal_requirements(self):
        """更新期刊要求显示"""
        journal = self.journal_style_var.get()
        requirements = self.academic_plot_manager.get_journal_requirements(journal)
        
        req_text = f"""
{journal.upper()} 期刊图表要求:

最大宽度: {requirements['max_width_inches']} 英寸
最大高度: {requirements['max_height_inches']} 英寸
最小字体: {requirements['min_font_size']} pt
推荐格式: {', '.join(requirements['preferred_formats'])}
分辨率: {requirements['dpi']} DPI

颜色配置: 已优化为期刊标准色彩
        """
        
        self.requirements_text.config(state=tk.NORMAL)
        self.requirements_text.delete(1.0, tk.END)
        self.requirements_text.insert(1.0, req_text.strip())
        self.requirements_text.config(state=tk.DISABLED)
    
    def _setup_academic_style(self):
        """设置学术样式"""
        try:
            from ....utils.academic_plots import setup_academic_matplotlib
            setup_academic_matplotlib()
            messagebox.showinfo("成功", "学术级matplotlib样式已设置")
            self.status_var.set("学术样式已应用")
            
        except Exception as e:
            messagebox.showerror("错误", f"设置学术样式失败: {e}")
    
    def _check_figure_compliance(self):
        """检查图表规范"""
        journal = self.journal_style_var.get()
        requirements = self.academic_plot_manager.get_journal_requirements(journal)
        
        compliance_info = f"""
图表规范检查 - {journal.upper()}:

✓ 字体: Arial/Helvetica (学术标准)
✓ 分辨率: 300 DPI (符合要求)
✓ 格式: PDF/PNG/SVG (支持多格式)
✓ 颜色: 期刊标准色彩配置
✓ 尺寸: 可调整至期刊要求

建议:
- 确保图表标题简洁明了
- 轴标签使用标准单位
- 图例位置合理
- 避免使用过多颜色
        """
        
        messagebox.showinfo("图表规范检查", compliance_info)
    
    def get_frame(self):
        """获取主框架"""
        return self.frame
