#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeLong检验GUI模块
提供DeLong统计检验的图形化界面
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
import seaborn as sns
import threading
from typing import Dict, List, Optional, Any

from ....utils.delong_test import get_delong_test, perform_delong_comparison
from ....core.event_manager import get_event_manager
from ....core.model_manager import get_model_manager
from ....utils.error_handler import get_error_handler


class DeLongTestGUI:
    """DeLong检验GUI"""
    
    def __init__(self, parent):
        """初始化DeLong检验GUI"""
        self.parent = parent
        self.delong_test = get_delong_test()
        self.event_manager = get_event_manager()
        self.model_manager = get_model_manager()
        self.error_handler = get_error_handler()
        
        # 数据存储
        self.model_results = {}
        self.delong_results = None
        
        # GUI组件
        self.frame = None
        
        # 控制变量
        self.alpha_var = tk.DoubleVar(value=0.05)
        self.status_var = tk.StringVar(value="就绪")
        
        # 创建界面
        self._create_interface()
        
        # 注册事件监听
        self._register_events()
        
        # 初始化数据
        self._refresh_models()
    
    def _create_interface(self):
        """创建界面"""
        # 主框架
        self.frame = ttk.Frame(self.parent)
        self.frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 标题
        title_frame = ttk.Frame(self.frame)
        title_frame.pack(fill=tk.X, pady=(0, 20))
        
        ttk.Label(title_frame, text="DeLong检验", font=('Arial', 16, 'bold')).pack()
        ttk.Label(title_frame, text="比较多个模型ROC曲线的统计显著性差异", 
                 foreground="gray").pack()
        
        # 创建笔记本控件
        self.notebook = ttk.Notebook(self.frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 创建选项卡
        self._create_model_selection_tab()
        self._create_results_tab()
    
    def _create_model_selection_tab(self):
        """创建模型选择选项卡"""
        selection_tab = ttk.Frame(self.notebook)
        self.notebook.add(selection_tab, text="🎯 模型选择")
        
        # 模型选择区域
        models_frame = ttk.LabelFrame(selection_tab, text="选择要比较的模型")
        models_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 模型列表框架
        list_frame = ttk.Frame(models_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧：可用模型列表
        left_frame = ttk.Frame(list_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        ttk.Label(left_frame, text="可用模型:").pack(anchor=tk.W)
        
        self.available_listbox = tk.Listbox(left_frame, selectmode=tk.MULTIPLE, height=15)
        available_scrollbar = ttk.Scrollbar(left_frame, orient=tk.VERTICAL, command=self.available_listbox.yview)
        self.available_listbox.configure(yscrollcommand=available_scrollbar.set)
        
        self.available_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        available_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 中间：操作按钮
        middle_frame = ttk.Frame(list_frame)
        middle_frame.pack(side=tk.LEFT, padx=10)
        
        ttk.Button(middle_frame, text="➡️", command=self._add_selected_models).pack(pady=5)
        ttk.Button(middle_frame, text="⬅️", command=self._remove_selected_models).pack(pady=5)
        ttk.Button(middle_frame, text="➡️➡️", command=self._add_all_models).pack(pady=5)
        ttk.Button(middle_frame, text="⬅️⬅️", command=self._remove_all_models).pack(pady=5)
        
        # 右侧：选中模型列表
        right_frame = ttk.Frame(list_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))
        
        ttk.Label(right_frame, text="选中模型:").pack(anchor=tk.W)
        
        self.selected_listbox = tk.Listbox(right_frame, selectmode=tk.MULTIPLE, height=15)
        selected_scrollbar = ttk.Scrollbar(right_frame, orient=tk.VERTICAL, command=self.selected_listbox.yview)
        self.selected_listbox.configure(yscrollcommand=selected_scrollbar.set)
        
        self.selected_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        selected_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 参数设置
        params_frame = ttk.LabelFrame(selection_tab, text="检验参数")
        params_frame.pack(fill=tk.X, padx=10, pady=10)
        
        param_row = ttk.Frame(params_frame)
        param_row.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(param_row, text="显著性水平 (α):").pack(side=tk.LEFT, padx=(0, 10))
        alpha_spinbox = tk.Spinbox(param_row, textvariable=self.alpha_var, 
                                  from_=0.001, to=0.1, increment=0.001, 
                                  format="%.3f", width=10)
        alpha_spinbox.pack(side=tk.LEFT, padx=(0, 20))
        
        ttk.Label(param_row, text="(常用值: 0.05, 0.01, 0.001)").pack(side=tk.LEFT)
        
        # 操作按钮
        control_frame = ttk.Frame(selection_tab)
        control_frame.pack(fill=tk.X, padx=10, pady=20)
        
        ttk.Button(control_frame, text="🔄 刷新模型", 
                  command=self._refresh_models).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(control_frame, text="🧪 开始检验", 
                  command=self._start_delong_test, style="Accent.TButton").pack(side=tk.LEFT, padx=(0, 10))
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(control_frame, variable=self.progress_var,
                                          mode='determinate', length=200)
        self.progress_bar.pack(side=tk.RIGHT, padx=(10, 0))
        
        # 状态栏
        status_frame = ttk.Frame(selection_tab)
        status_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        ttk.Label(status_frame, text="状态:").pack(side=tk.LEFT)
        ttk.Label(status_frame, textvariable=self.status_var, foreground="blue").pack(
            side=tk.LEFT, padx=(5, 0))
    
    def _create_results_tab(self):
        """创建结果选项卡"""
        results_tab = ttk.Frame(self.notebook)
        self.notebook.add(results_tab, text="📊 检验结果")
        
        # 结果摘要
        summary_frame = ttk.LabelFrame(results_tab, text="检验摘要")
        summary_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.summary_text = tk.Text(summary_frame, height=6, wrap=tk.WORD, 
                                   font=('Consolas', 10), state=tk.DISABLED)
        summary_scrollbar = ttk.Scrollbar(summary_frame, orient=tk.VERTICAL, command=self.summary_text.yview)
        self.summary_text.configure(yscrollcommand=summary_scrollbar.set)
        
        self.summary_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        summary_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
        # 详细结果
        details_frame = ttk.LabelFrame(results_tab, text="详细结果")
        details_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建笔记本控件用于不同类型的结果
        self.results_notebook = ttk.Notebook(details_frame)
        self.results_notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 两两比较结果选项卡
        self._create_pairwise_tab()
        
        # 可视化选项卡
        self._create_visualization_tab()
        
        # 操作按钮
        actions_frame = ttk.Frame(results_tab)
        actions_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(actions_frame, text="💾 保存结果", 
                  command=self._save_results).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(actions_frame, text="📊 生成报告", 
                  command=self._generate_report).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(actions_frame, text="📈 导出图表", 
                  command=self._export_charts).pack(side=tk.LEFT, padx=(0, 10))
    
    def _create_pairwise_tab(self):
        """创建两两比较结果选项卡"""
        pairwise_tab = ttk.Frame(self.results_notebook)
        self.results_notebook.add(pairwise_tab, text="📋 两两比较")
        
        # 创建结果表格
        columns = ('模型1', '模型2', 'AUC1', 'AUC2', 'AUC差异', 'Z分数', 'P值', '显著性', '更优模型')
        self.results_tree = ttk.Treeview(pairwise_tab, columns=columns, show='headings', height=15)
        
        # 设置列标题和宽度
        column_widths = {
            '模型1': 100, '模型2': 100, 'AUC1': 80, 'AUC2': 80, 'AUC差异': 80,
            'Z分数': 80, 'P值': 80, '显著性': 80, '更优模型': 100
        }
        
        for col in columns:
            self.results_tree.heading(col, text=col)
            self.results_tree.column(col, width=column_widths.get(col, 80), anchor=tk.CENTER)
        
        # 滚动条
        results_scrollbar = ttk.Scrollbar(pairwise_tab, orient=tk.VERTICAL, command=self.results_tree.yview)
        self.results_tree.configure(yscrollcommand=results_scrollbar.set)
        
        self.results_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        results_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
    
    def _create_visualization_tab(self):
        """创建可视化选项卡"""
        viz_tab = ttk.Frame(self.results_notebook)
        self.results_notebook.add(viz_tab, text="📈 可视化")
        
        # 图表类型选择
        chart_control_frame = ttk.Frame(viz_tab)
        chart_control_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(chart_control_frame, text="图表类型:").pack(side=tk.LEFT, padx=(0, 10))
        
        self.chart_type_var = tk.StringVar(value="P值热力图")
        chart_types = ["P值热力图", "Z分数热力图", "AUC比较图", "显著性矩阵"]
        self.chart_combo = ttk.Combobox(chart_control_frame, textvariable=self.chart_type_var,
                                       values=chart_types, state="readonly", width=15)
        self.chart_combo.pack(side=tk.LEFT, padx=(0, 10))
        self.chart_combo.bind('<<ComboboxSelected>>', self._on_chart_type_change)
        
        ttk.Button(chart_control_frame, text="🔄 刷新图表", 
                  command=self._refresh_chart).pack(side=tk.LEFT, padx=(10, 0))
        
        # 图表显示区域
        self.viz_fig, self.viz_ax = plt.subplots(figsize=(10, 8))
        self.viz_fig.patch.set_facecolor('white')
        
        self.viz_canvas = FigureCanvasTkAgg(self.viz_fig, viz_tab)
        self.viz_canvas.draw()
        self.viz_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 工具栏
        viz_toolbar_frame = ttk.Frame(viz_tab)
        viz_toolbar_frame.pack(fill=tk.X)
        self.viz_toolbar = NavigationToolbar2Tk(self.viz_canvas, viz_toolbar_frame)
        self.viz_toolbar.update()
        
        # 初始化空白图表
        self._show_empty_chart()
    
    def _register_events(self):
        """注册事件监听"""
        self.event_manager.subscribe('model_trained', self._on_model_trained)
    
    def _on_model_trained(self, event_data):
        """模型训练完成事件处理"""
        self._refresh_models()
    
    def _refresh_models(self):
        """刷新模型列表"""
        try:
            # 清空列表
            self.available_listbox.delete(0, tk.END)
            
            # 获取所有可用模型
            all_models = set()
            
            # 从模型管理器获取
            manager_models = self.model_manager.list_models()
            for model_name in manager_models:
                result = self.model_manager.get_model_result(model_name)
                if result and 'y_true' in result and 'y_pred_proba' in result:
                    # 准备DeLong检验所需的数据格式
                    self.model_results[model_name] = {
                        'y_true': result['y_true'],
                        'y_scores': result['y_pred_proba']
                    }
                    all_models.add(model_name)
            
            # 添加到可用列表
            for model_name in sorted(all_models):
                self.available_listbox.insert(tk.END, model_name)
            
            self.status_var.set(f"已加载 {len(all_models)} 个可比较模型")
            
        except Exception as e:
            self.error_handler.handle_error(e, "刷新模型列表")
    
    def _add_selected_models(self):
        """添加选中的模型"""
        selected_indices = self.available_listbox.curselection()
        for index in selected_indices:
            model_name = self.available_listbox.get(index)
            if model_name not in [self.selected_listbox.get(i) for i in range(self.selected_listbox.size())]:
                self.selected_listbox.insert(tk.END, model_name)
        
        self._update_selection_status()
    
    def _remove_selected_models(self):
        """移除选中的模型"""
        selected_indices = list(self.selected_listbox.curselection())
        selected_indices.reverse()
        for index in selected_indices:
            self.selected_listbox.delete(index)
        
        self._update_selection_status()
    
    def _add_all_models(self):
        """添加所有模型"""
        self.selected_listbox.delete(0, tk.END)
        for i in range(self.available_listbox.size()):
            model_name = self.available_listbox.get(i)
            self.selected_listbox.insert(tk.END, model_name)
        
        self._update_selection_status()
    
    def _remove_all_models(self):
        """移除所有模型"""
        self.selected_listbox.delete(0, tk.END)
        self._update_selection_status()
    
    def _update_selection_status(self):
        """更新选择状态"""
        selected_count = self.selected_listbox.size()
        self.status_var.set(f"已选择 {selected_count} 个模型进行DeLong检验")
    
    def _start_delong_test(self):
        """开始DeLong检验"""
        selected_models = [self.selected_listbox.get(i) for i in range(self.selected_listbox.size())]
        
        if len(selected_models) < 2:
            messagebox.showwarning("警告", "请至少选择2个模型进行比较")
            return
        
        alpha = self.alpha_var.get()
        if alpha <= 0 or alpha >= 1:
            messagebox.showwarning("警告", "显著性水平必须在0和1之间")
            return
        
        # 在后台线程中执行检验
        threading.Thread(target=self._perform_delong_test, 
                        args=(selected_models, alpha), daemon=True).start()
    
    def _perform_delong_test(self, selected_models: List[str], alpha: float):
        """执行DeLong检验"""
        try:
            self.status_var.set("正在进行DeLong检验...")
            self.progress_var.set(20)
            
            # 准备模型数据
            model_data = {}
            for model_name in selected_models:
                if model_name in self.model_results:
                    model_data[model_name] = self.model_results[model_name]
            
            if len(model_data) < 2:
                messagebox.showerror("错误", "无法获取足够的模型数据进行比较")
                return
            
            self.progress_var.set(50)
            
            # 执行DeLong检验
            self.delong_results = perform_delong_comparison(model_data, alpha)
            
            self.progress_var.set(80)
            
            # 显示结果
            self._display_delong_results()
            
            self.progress_var.set(100)
            self.status_var.set("DeLong检验完成")
            
            # 切换到结果选项卡
            self.notebook.select(1)
            
            # 重置进度条
            self.parent.after(2000, lambda: self.progress_var.set(0))
            
        except Exception as e:
            self.error_handler.handle_error(e, "DeLong检验")
            self.status_var.set("DeLong检验失败")
            self.progress_var.set(0)
            messagebox.showerror("错误", f"DeLong检验失败: {e}")
    
    def _display_delong_results(self):
        """显示DeLong检验结果"""
        if not self.delong_results:
            return
        
        try:
            # 显示摘要
            self._display_summary()
            
            # 显示详细结果
            self._display_pairwise_results()
            
            # 生成可视化
            self._refresh_chart()
            
        except Exception as e:
            self.error_handler.handle_error(e, "显示DeLong检验结果")
    
    def _display_summary(self):
        """显示检验摘要"""
        summary = self.delong_results['summary']
        
        summary_text = f"""DeLong检验摘要:

比较模型数量: {len(summary['model_names'])}
总比较次数: {summary['total_comparisons']}
显著差异比较: {summary['significant_comparisons']}
显著性水平: {summary['alpha']}

模型列表: {', '.join(summary['model_names'])}

结论: 在α={summary['alpha']}的显著性水平下，
{summary['significant_comparisons']}/{summary['total_comparisons']} 对模型存在显著差异。
"""
        
        self.summary_text.config(state=tk.NORMAL)
        self.summary_text.delete(1.0, tk.END)
        self.summary_text.insert(1.0, summary_text)
        self.summary_text.config(state=tk.DISABLED)
    
    def _display_pairwise_results(self):
        """显示两两比较结果"""
        # 清空现有结果
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)
        
        # 添加比较结果
        for result in self.delong_results['pairwise_results']:
            significance = "显著" if result['is_significant'] else "不显著"
            
            self.results_tree.insert('', 'end', values=(
                result['model1'],
                result['model2'],
                f"{result['auc1']:.4f}",
                f"{result['auc2']:.4f}",
                f"{result['auc_diff']:.4f}",
                f"{result['z_score']:.4f}",
                f"{result['p_value']:.4f}",
                significance,
                result['better_model']
            ))
    
    def _show_empty_chart(self):
        """显示空白图表"""
        self.viz_ax.clear()
        self.viz_ax.text(0.5, 0.5, '请完成DeLong检验后查看可视化结果', 
                        ha='center', va='center', transform=self.viz_ax.transAxes,
                        fontsize=14, color='gray')
        self.viz_ax.set_xticks([])
        self.viz_ax.set_yticks([])
        self.viz_canvas.draw()
    
    def _on_chart_type_change(self, event=None):
        """图表类型改变事件处理"""
        self._refresh_chart()
    
    def _refresh_chart(self):
        """刷新图表"""
        if not self.delong_results:
            self._show_empty_chart()
            return
        
        chart_type = self.chart_type_var.get()
        
        try:
            self.viz_ax.clear()
            
            if chart_type == "P值热力图":
                self._plot_p_value_heatmap()
            elif chart_type == "Z分数热力图":
                self._plot_z_score_heatmap()
            elif chart_type == "AUC比较图":
                self._plot_auc_comparison()
            elif chart_type == "显著性矩阵":
                self._plot_significance_matrix()
            
            self.viz_canvas.draw()
            
        except Exception as e:
            self.error_handler.handle_error(e, "刷新图表")
            self._show_empty_chart()
    
    def _plot_p_value_heatmap(self):
        """绘制P值热力图"""
        p_matrix = np.array(self.delong_results['p_value_matrix'])
        model_names = self.delong_results['summary']['model_names']
        
        # 创建热力图
        sns.heatmap(p_matrix, annot=True, fmt='.4f', cmap='RdYlBu_r',
                   xticklabels=model_names, yticklabels=model_names,
                   ax=self.viz_ax, cbar_kws={'label': 'P值'})
        
        self.viz_ax.set_title('DeLong检验P值热力图')
        self.viz_ax.set_xlabel('模型')
        self.viz_ax.set_ylabel('模型')
        
        # 添加显著性水平线
        alpha = self.delong_results['summary']['alpha']
        self.viz_ax.text(0.02, 0.98, f'显著性水平: α = {alpha}', 
                        transform=self.viz_ax.transAxes, 
                        bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7))
    
    def _plot_z_score_heatmap(self):
        """绘制Z分数热力图"""
        z_matrix = np.array(self.delong_results['comparison_matrix'])
        model_names = self.delong_results['summary']['model_names']
        
        # 创建热力图
        sns.heatmap(z_matrix, annot=True, fmt='.2f', cmap='RdBu_r', center=0,
                   xticklabels=model_names, yticklabels=model_names,
                   ax=self.viz_ax, cbar_kws={'label': 'Z分数'})
        
        self.viz_ax.set_title('DeLong检验Z分数热力图')
        self.viz_ax.set_xlabel('模型')
        self.viz_ax.set_ylabel('模型')
    
    def _plot_auc_comparison(self):
        """绘制AUC比较图"""
        # 提取AUC值
        model_aucs = {}
        for result in self.delong_results['pairwise_results']:
            model_aucs[result['model1']] = result['auc1']
            model_aucs[result['model2']] = result['auc2']
        
        models = list(model_aucs.keys())
        aucs = list(model_aucs.values())
        
        # 创建条形图
        bars = self.viz_ax.bar(models, aucs, alpha=0.7, color='skyblue', edgecolor='navy')
        
        # 添加数值标签
        for bar, auc in zip(bars, aucs):
            height = bar.get_height()
            self.viz_ax.text(bar.get_x() + bar.get_width()/2., height + 0.001,
                           f'{auc:.4f}', ha='center', va='bottom')
        
        self.viz_ax.set_title('模型AUC比较')
        self.viz_ax.set_xlabel('模型')
        self.viz_ax.set_ylabel('AUC')
        self.viz_ax.set_ylim(0, 1.1)
        
        # 旋转x轴标签
        plt.setp(self.viz_ax.get_xticklabels(), rotation=45, ha='right')
    
    def _plot_significance_matrix(self):
        """绘制显著性矩阵"""
        p_matrix = np.array(self.delong_results['p_value_matrix'])
        model_names = self.delong_results['summary']['model_names']
        alpha = self.delong_results['summary']['alpha']
        
        # 创建显著性矩阵（1表示显著，0表示不显著）
        sig_matrix = (p_matrix < alpha).astype(int)
        
        # 创建热力图
        sns.heatmap(sig_matrix, annot=True, fmt='d', cmap='RdYlGn',
                   xticklabels=model_names, yticklabels=model_names,
                   ax=self.viz_ax, cbar_kws={'label': '显著性 (1=显著, 0=不显著)'})
        
        self.viz_ax.set_title(f'显著性差异矩阵 (α = {alpha})')
        self.viz_ax.set_xlabel('模型')
        self.viz_ax.set_ylabel('模型')
    
    def _save_results(self):
        """保存结果"""
        if not self.delong_results:
            messagebox.showwarning("警告", "没有可保存的检验结果")
            return
        
        filename = filedialog.asksaveasfilename(
            title="保存DeLong检验结果",
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("CSV文件", "*.csv"), ("所有文件", "*.*")]
        )
        
        if filename:
            try:
                if filename.endswith('.json'):
                    self.delong_test.save_results(self.delong_results, filename)
                elif filename.endswith('.csv'):
                    # 保存两两比较结果为CSV
                    df = pd.DataFrame(self.delong_results['pairwise_results'])
                    df.to_csv(filename, index=False)
                
                messagebox.showinfo("成功", f"DeLong检验结果已保存到: {filename}")
                
            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {e}")
    
    def _generate_report(self):
        """生成报告"""
        if not self.delong_results:
            messagebox.showwarning("警告", "没有可生成报告的结果")
            return
        
        try:
            report = self.delong_test.get_summary_report(self.delong_results)
            
            # 创建报告窗口
            report_window = tk.Toplevel(self.parent)
            report_window.title("DeLong检验报告")
            report_window.geometry("800x600")
            
            # 创建文本框显示报告
            text_widget = tk.Text(report_window, wrap=tk.WORD, font=('Consolas', 10))
            scrollbar = ttk.Scrollbar(report_window, orient=tk.VERTICAL, command=text_widget.yview)
            text_widget.configure(yscrollcommand=scrollbar.set)
            
            text_widget.insert(1.0, report)
            text_widget.config(state=tk.DISABLED)
            
            text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
            
        except Exception as e:
            messagebox.showerror("错误", f"生成报告失败: {e}")
    
    def _export_charts(self):
        """导出图表"""
        filename = filedialog.asksaveasfilename(
            title="导出DeLong检验图表",
            defaultextension=".png",
            filetypes=[("PNG文件", "*.png"), ("PDF文件", "*.pdf"), ("SVG文件", "*.svg")]
        )
        
        if filename:
            try:
                self.viz_fig.savefig(filename, dpi=300, bbox_inches='tight')
                messagebox.showinfo("成功", f"图表已导出到: {filename}")
            except Exception as e:
                messagebox.showerror("错误", f"导出失败: {e}")
    
    def get_frame(self):
        """获取主框架"""
        return self.frame
