#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
事件管理器模块
实现模块间解耦的事件驱动通信机制
"""

from collections import defaultdict
from typing import Callable, Any, Dict, List
import logging
from threading import Lock


class EventManager:
    """
    事件管理器
    提供发布-订阅模式的事件通信机制，实现模块间解耦
    """
    
    def __init__(self):
        """初始化事件管理器"""
        self.listeners: Dict[str, List[Callable]] = defaultdict(list)
        self.lock = Lock()
        self.logger = logging.getLogger(__name__)
        
    def subscribe(self, event_type: str, callback: Callable) -> None:
        """
        订阅事件
        
        Args:
            event_type: 事件类型
            callback: 回调函数
        """
        with self.lock:
            if callback not in self.listeners[event_type]:
                self.listeners[event_type].append(callback)
                self.logger.debug(f"订阅事件: {event_type}")
    
    def unsubscribe(self, event_type: str, callback: Callable) -> None:
        """
        取消订阅事件
        
        Args:
            event_type: 事件类型
            callback: 回调函数
        """
        with self.lock:
            if callback in self.listeners[event_type]:
                self.listeners[event_type].remove(callback)
                self.logger.debug(f"取消订阅事件: {event_type}")
    
    def publish(self, event_type: str, data: Any = None) -> None:
        """
        发布事件
        
        Args:
            event_type: 事件类型
            data: 事件数据
        """
        with self.lock:
            callbacks = self.listeners[event_type].copy()
        
        self.logger.debug(f"发布事件: {event_type}, 监听器数量: {len(callbacks)}")
        
        for callback in callbacks:
            try:
                callback(data)
            except Exception as e:
                self.logger.error(f"事件回调执行失败: {event_type}, 错误: {e}")
    
    def clear_listeners(self, event_type: str = None) -> None:
        """
        清除监听器
        
        Args:
            event_type: 事件类型，如果为None则清除所有
        """
        with self.lock:
            if event_type:
                self.listeners[event_type].clear()
                self.logger.debug(f"清除事件监听器: {event_type}")
            else:
                self.listeners.clear()
                self.logger.debug("清除所有事件监听器")
    
    def get_listener_count(self, event_type: str) -> int:
        """
        获取指定事件的监听器数量
        
        Args:
            event_type: 事件类型
            
        Returns:
            监听器数量
        """
        return len(self.listeners[event_type])


# 全局事件管理器实例
_global_event_manager = None


def get_event_manager() -> EventManager:
    """获取全局事件管理器实例"""
    global _global_event_manager
    if _global_event_manager is None:
        _global_event_manager = EventManager()
    return _global_event_manager


# 常用事件类型定义
class EventTypes:
    """事件类型常量定义"""
    
    # 数据相关事件
    DATA_LOADED = "data_loaded"
    DATA_PREPROCESSED = "data_preprocessed"
    DATA_VALIDATED = "data_validated"
    
    # 模型相关事件
    MODEL_TRAINING_STARTED = "model_training_started"
    MODEL_TRAINING_PROGRESS = "model_training_progress"
    MODEL_TRAINING_COMPLETED = "model_training_completed"
    MODEL_TRAINING_FAILED = "model_training_failed"
    MODEL_TRAINED = "model_trained"
    
    # 可视化相关事件
    CHART_GENERATED = "chart_generated"
    CHART_UPDATED = "chart_updated"
    VISUALIZATION_REQUESTED = "visualization_requested"
    
    # 集成学习相关事件
    ENSEMBLE_STARTED = "ensemble_started"
    ENSEMBLE_COMPLETED = "ensemble_completed"
    
    # 会话相关事件
    SESSION_CREATED = "session_created"
    SESSION_LOADED = "session_loaded"
    SESSION_SAVED = "session_saved"
    SESSION_NEW = "session_new"
    SESSION_CHANGED = "session_changed"
    SESSION_CLEARED = "session_cleared"
    
    # GUI相关事件
    TAB_CHANGED = "tab_changed"
    WINDOW_RESIZED = "window_resized"
    CONFIG_UPDATED = "config_updated"
