#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
外部验证管理器
负责外部数据的模型验证和结果对比
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from typing import Dict, Any, Optional, List
import threading
import time
import pandas as pd
import numpy as np
from pathlib import Path

from ...core.base_gui import BaseGUI
from ...core.component_factory import get_component_factory
from ...core.event_manager import get_event_manager, EventTypes
from ...components.progress_widgets import ProgressWidget

# 导入算法模块
try:
    from ....algorithms.external_validation import ExternalValidator, run_external_validation
    HAS_ALGORITHMS = True
except ImportError:
    HAS_ALGORITHMS = False


class ValidationManager(BaseGUI):
    """外部验证管理器类"""
    
    def __init__(self, parent):
        """初始化外部验证管理器"""
        self.current_data = None
        self.external_data = None
        self.training_results = None
        self.validation_results = {}
        self.selected_models = []
        self.validation_config = {
            'target_column': None,
            'feature_columns': None,
            'preprocessing': True,
            'scaling_method': 'standard'
        }
        self.is_validating = False
        
        super().__init__(parent)
        
        # 订阅相关事件
        self._bind_events()
    
    def _setup_ui(self):
        """设置UI界面"""
        factory = get_component_factory()
        
        # 主框架
        if self.parent:
            self.main_frame = factory.create_frame(self.parent)
            self.main_frame.pack(fill='both', expand=True, padx=5, pady=5)
        else:
            self.main_frame = None
            return
        
        # 创建主要内容区域
        self._create_main_content()
    
    def _create_main_content(self):
        """创建主要内容区域"""
        factory = get_component_factory()
        
        # 创建水平分割面板
        paned_window = ttk.PanedWindow(self.main_frame, orient=tk.HORIZONTAL)
        paned_window.pack(fill='both', expand=True)
        
        # 左侧配置面板
        self._create_config_panel(paned_window)
        
        # 右侧结果面板
        self._create_results_panel(paned_window)
        
        self.register_component('paned_window', paned_window)
    
    def _create_config_panel(self, parent):
        """创建左侧配置面板"""
        factory = get_component_factory()
        
        # 配置面板框架
        config_frame = factory.create_frame(parent)
        config_frame.pack(fill='both', expand=True)
        
        # 创建滚动区域
        canvas = tk.Canvas(config_frame, width=400)
        scrollbar = ttk.Scrollbar(config_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = factory.create_frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 状态信息
        self._create_status_section(scrollable_frame)
        
        # 外部数据加载
        self._create_data_loading_section(scrollable_frame)
        
        # 模型选择
        self._create_model_selection_section(scrollable_frame)
        
        # 验证配置
        self._create_validation_config_section(scrollable_frame)
        
        # 控制按钮
        self._create_control_buttons_section(scrollable_frame)
        
        # 将配置面板添加到分割窗口
        parent.add(config_frame, weight=1)
        
        self.register_component('config_frame', config_frame)
    
    def _create_status_section(self, parent):
        """创建状态信息区域"""
        factory = get_component_factory()
        
        status_frame = factory.create_labelframe(parent, text="✅ 验证状态")
        status_frame.pack(fill='x', padx=10, pady=5)
        
        self.status_label = factory.create_label(status_frame, text="等待外部数据加载...", style='info')
        self.status_label.pack(padx=10, pady=10)
        
        # 进度条
        self.progress_widget = ProgressWidget(status_frame, show_percentage=True)
        if self.progress_widget.main_frame:
            self.progress_widget.main_frame.pack(fill='x', padx=10, pady=(0, 10))
    
    def _create_data_loading_section(self, parent):
        """创建外部数据加载区域"""
        factory = get_component_factory()
        
        data_frame = factory.create_labelframe(parent, text="📁 外部数据加载")
        data_frame.pack(fill='x', padx=10, pady=5)
        
        # 文件路径选择
        path_frame = factory.create_frame(data_frame)
        path_frame.pack(fill='x', padx=10, pady=5)
        
        factory.create_label(path_frame, text="数据文件:").pack(side='left')
        self.file_path_var = tk.StringVar()
        path_entry = factory.create_entry(path_frame, textvariable=self.file_path_var, width=25)
        path_entry.pack(side='left', fill='x', expand=True, padx=(10, 5))
        
        browse_btn = factory.create_button(path_frame, text="浏览", command=self._browse_file, style='small')
        browse_btn.pack(side='right')
        
        # 加载按钮
        load_frame = factory.create_frame(data_frame)
        load_frame.pack(fill='x', padx=10, pady=5)
        
        self.load_data_btn = factory.create_button(
            load_frame, text="📂 加载外部数据", 
            command=self._load_external_data, style='primary'
        )
        self.load_data_btn.pack(fill='x')
        
        # 数据信息显示
        self.data_info_label = factory.create_label(
            data_frame, text="请选择外部验证数据文件", style='secondary'
        )
        self.data_info_label.pack(padx=10, pady=5)
    
    def _create_model_selection_section(self, parent):
        """创建模型选择区域"""
        factory = get_component_factory()
        
        model_frame = factory.create_labelframe(parent, text="🎯 模型选择")
        model_frame.pack(fill='x', padx=10, pady=5)
        
        # 说明文本
        info_label = factory.create_label(
            model_frame, 
            text="选择要进行外部验证的模型:",
            style='secondary'
        )
        info_label.pack(padx=10, pady=5, anchor='w')
        
        # 模型复选框容器
        self.model_checkboxes_frame = factory.create_frame(model_frame)
        self.model_checkboxes_frame.pack(fill='x', padx=10, pady=5)
        
        # 模型变量字典
        self.model_vars = {}
        
        # 占位符文本
        self.no_models_label = factory.create_label(
            self.model_checkboxes_frame,
            text="请先完成模型训练",
            style='secondary'
        )
        self.no_models_label.pack(pady=10)
        
        # 全选/取消全选按钮
        button_frame = factory.create_frame(model_frame)
        button_frame.pack(fill='x', padx=10, pady=5)
        
        self.select_all_btn = factory.create_button(button_frame, text="全选", 
                                                  command=self._select_all_models, style='small')
        self.select_all_btn.pack(side='left', padx=(0, 5))
        self.select_all_btn.config(state='disabled')
        
        self.deselect_all_btn = factory.create_button(button_frame, text="取消全选", 
                                                    command=self._deselect_all_models, style='small')
        self.deselect_all_btn.pack(side='left')
        self.deselect_all_btn.config(state='disabled')
    
    def _create_validation_config_section(self, parent):
        """创建验证配置区域"""
        factory = get_component_factory()
        
        config_frame = factory.create_labelframe(parent, text="⚙️ 验证配置")
        config_frame.pack(fill='x', padx=10, pady=5)
        
        # 目标列选择
        target_frame = factory.create_frame(config_frame)
        target_frame.pack(fill='x', padx=10, pady=5)
        
        factory.create_label(target_frame, text="目标列:").pack(side='left')
        self.target_column_var = tk.StringVar()
        self.target_column_combo = ttk.Combobox(target_frame, textvariable=self.target_column_var,
                                              state='readonly', width=20)
        self.target_column_combo.pack(side='right')
        
        # 预处理选项
        preprocessing_frame = factory.create_frame(config_frame)
        preprocessing_frame.pack(fill='x', padx=10, pady=5)
        
        self.preprocessing_var = tk.BooleanVar(value=True)
        preprocessing_cb = factory.create_checkbutton(preprocessing_frame, text="启用数据预处理", 
                                                    variable=self.preprocessing_var)
        preprocessing_cb.pack(side='left')
        
        # 缩放方法
        scaling_frame = factory.create_frame(config_frame)
        scaling_frame.pack(fill='x', padx=10, pady=5)
        
        factory.create_label(scaling_frame, text="特征缩放:").pack(side='left')
        self.scaling_method_var = tk.StringVar(value='standard')
        scaling_combo = ttk.Combobox(scaling_frame, textvariable=self.scaling_method_var,
                                   values=['standard', 'minmax', 'robust', 'none'],
                                   state='readonly', width=15)
        scaling_combo.pack(side='right')
    
    def _create_control_buttons_section(self, parent):
        """创建控制按钮区域"""
        factory = get_component_factory()
        
        button_frame = factory.create_frame(parent)
        button_frame.pack(fill='x', padx=10, pady=10)
        
        # 开始验证按钮
        self.validation_button = factory.create_button(
            button_frame, 
            text="🚀 开始外部验证", 
            command=self._start_validation,
            style='primary'
        )
        self.validation_button.pack(fill='x', pady=(0, 5))
        self.validation_button.config(state='disabled')
        
        # 停止验证按钮
        self.stop_button = factory.create_button(
            button_frame, 
            text="⏹️ 停止验证", 
            command=self._stop_validation,
            style='danger'
        )
        self.stop_button.pack(fill='x', pady=(0, 5))
        self.stop_button.config(state='disabled')
        
        # 清空结果按钮
        clear_button = factory.create_button(
            button_frame, 
            text="🗑️ 清空结果", 
            command=self.clear_results,
            style='secondary'
        )
        clear_button.pack(fill='x')

    def _create_results_panel(self, parent):
        """创建右侧结果面板"""
        factory = get_component_factory()

        # 结果面板框架
        results_frame = factory.create_frame(parent)
        results_frame.pack(fill='both', expand=True)

        # 创建标签页
        self.results_notebook = factory.create_notebook(results_frame)
        self.results_notebook.pack(fill='both', expand=True, padx=5, pady=5)

        # 验证日志标签页
        self._create_validation_log_tab()

        # 验证结果标签页
        self._create_validation_results_tab()

        # 结果对比标签页
        self._create_comparison_tab()

        # 将结果面板添加到分割窗口
        parent.add(results_frame, weight=2)

        self.register_component('results_frame', results_frame)

    def _create_validation_log_tab(self):
        """创建验证日志标签页"""
        factory = get_component_factory()

        log_frame = factory.create_frame(self.results_notebook)
        self.results_notebook.add(log_frame, text="📋 验证日志")

        # 日志文本区域
        self.log_text = tk.Text(log_frame, wrap=tk.WORD, font=('Consolas', 9))
        log_scrollbar = ttk.Scrollbar(log_frame, orient='vertical', command=self.log_text.yview)
        self.log_text.config(yscrollcommand=log_scrollbar.set)

        self.log_text.pack(side='left', fill='both', expand=True, padx=(5, 0), pady=5)
        log_scrollbar.pack(side='right', fill='y', padx=(0, 5), pady=5)

        self.log_text.insert('1.0', "外部验证日志将在这里显示...\n")
        self.log_text.config(state='disabled')

    def _create_validation_results_tab(self):
        """创建验证结果标签页"""
        factory = get_component_factory()

        results_frame = factory.create_frame(self.results_notebook)
        self.results_notebook.add(results_frame, text="📊 验证结果")

        # 结果表格
        columns = ('模型', '内部准确率', '外部准确率', '性能差异', 'F1分数', 'AUC')
        self.results_tree = ttk.Treeview(results_frame, columns=columns, show='headings', height=15)

        # 设置列标题
        for col in columns:
            self.results_tree.heading(col, text=col)
            self.results_tree.column(col, width=120, anchor='center')

        # 滚动条
        results_scrollbar = ttk.Scrollbar(results_frame, orient='vertical', command=self.results_tree.yview)
        self.results_tree.config(yscrollcommand=results_scrollbar.set)

        self.results_tree.pack(side='left', fill='both', expand=True, padx=(5, 0), pady=5)
        results_scrollbar.pack(side='right', fill='y', padx=(0, 5), pady=5)

    def _create_comparison_tab(self):
        """创建结果对比标签页"""
        factory = get_component_factory()

        comparison_frame = factory.create_frame(self.results_notebook)
        self.results_notebook.add(comparison_frame, text="🔍 结果对比")

        # 对比图表区域（占位符）
        comparison_label = factory.create_label(
            comparison_frame,
            text="结果对比图表将在验证完成后显示\n\n包括：\n• 内部 vs 外部性能对比\n• ROC曲线比较\n• 混淆矩阵对比\n• 性能稳定性分析",
            style='secondary'
        )
        comparison_label.pack(expand=True)

    def _browse_file(self):
        """浏览文件"""
        file_path = filedialog.askopenfilename(
            title="选择外部验证数据文件",
            filetypes=[
                ("CSV files", "*.csv"),
                ("Excel files", "*.xlsx *.xls"),
                ("All files", "*.*")
            ]
        )

        if file_path:
            self.file_path_var.set(file_path)

    def _load_external_data(self):
        """加载外部数据"""
        file_path = self.file_path_var.get()
        if not file_path:
            self.show_warning("警告", "请先选择数据文件！")
            return

        try:
            # 加载数据
            if file_path.endswith('.csv'):
                self.external_data = pd.read_csv(file_path)
            elif file_path.endswith(('.xlsx', '.xls')):
                self.external_data = pd.read_excel(file_path)
            else:
                self.show_error("错误", "不支持的文件格式！")
                return

            # 更新数据信息显示
            rows, cols = self.external_data.shape
            self.data_info_label.config(text=f"✅ 数据已加载: {rows} 行, {cols} 列")

            # 更新目标列选择
            self.target_column_combo['values'] = list(self.external_data.columns)
            if len(self.external_data.columns) > 0:
                self.target_column_combo.set(self.external_data.columns[-1])  # 默认选择最后一列

            # 更新状态
            self.status_label.config(text=f"✅ 外部数据已加载: {rows} 行, {cols} 列")

            # 检查是否可以开始验证
            self._check_validation_ready()

            self._log_message(f"外部数据已加载: {file_path}")
            self._log_message(f"数据维度: {rows} 行, {cols} 列")

        except Exception as e:
            self.show_error("加载失败", f"加载外部数据时出错:\n{str(e)}")
            self._log_message(f"数据加载失败: {str(e)}")

    def _select_all_models(self):
        """全选所有模型"""
        for var in self.model_vars.values():
            var.set(True)

    def _deselect_all_models(self):
        """取消选择所有模型"""
        for var in self.model_vars.values():
            var.set(False)

    def _check_validation_ready(self):
        """检查是否可以开始验证"""
        has_external_data = self.external_data is not None
        has_models = self.training_results is not None and len(self.training_results) > 0

        if has_external_data and has_models:
            self.validation_button.config(state='normal')
        else:
            self.validation_button.config(state='disabled')

    def _start_validation(self):
        """开始外部验证"""
        if not self.external_data:
            self.show_warning("警告", "请先加载外部数据！")
            return

        if not self.training_results:
            self.show_warning("警告", "请先完成模型训练！")
            return

        # 获取选中的模型
        selected_models = [name for name, var in self.model_vars.items() if var.get()]
        if not selected_models:
            self.show_warning("警告", "请至少选择一个模型进行验证！")
            return

        # 检查目标列
        target_column = self.target_column_var.get()
        if not target_column:
            self.show_warning("警告", "请选择目标列！")
            return

        if target_column not in self.external_data.columns:
            self.show_error("错误", f"目标列 '{target_column}' 不存在于外部数据中！")
            return

        # 更新验证配置
        self.validation_config.update({
            'target_column': target_column,
            'feature_columns': [col for col in self.external_data.columns if col != target_column],
            'preprocessing': self.preprocessing_var.get(),
            'scaling_method': self.scaling_method_var.get()
        })

        # 更新UI状态
        self.is_validating = True
        self.status_label.config(text="正在进行外部验证...")
        self.validation_button.config(state='disabled')
        self.stop_button.config(state='normal')

        # 清空之前的结果
        self._clear_results_display()

        # 开始验证
        self._run_validation(selected_models)

    def _stop_validation(self):
        """停止外部验证"""
        self.is_validating = False
        self.status_label.config(text="外部验证已停止")
        self.validation_button.config(state='normal')
        self.stop_button.config(state='disabled')
        self._log_message("外部验证已被用户停止")

    def _run_validation(self, selected_models):
        """运行外部验证过程"""
        def validation():
            try:
                self._log_message(f"开始外部验证，选中模型: {', '.join(selected_models)}")
                self._log_message(f"验证配置: {self.validation_config}")

                # 准备外部数据
                target_col = self.validation_config['target_column']
                feature_cols = self.validation_config['feature_columns']

                X_external = self.external_data[feature_cols]
                y_external = self.external_data[target_col]

                self._log_message(f"外部验证数据: {X_external.shape[0]} 样本, {X_external.shape[1]} 特征")

                # 验证每个模型
                validation_results = {}
                total_models = len(selected_models)

                for i, model_name in enumerate(selected_models):
                    if not self.is_validating:
                        break

                    self._log_message(f"正在验证 {model_name} ({i+1}/{total_models})...")

                    # 更新进度
                    progress = (i / total_models) * 100
                    if self.main_frame:
                        self.main_frame.after(0, lambda p=progress: self.progress_widget.set_progress(p))

                    # 模拟验证过程
                    time.sleep(1)  # 模拟验证时间

                    # 获取内部验证结果
                    internal_result = self.training_results[model_name]
                    internal_metrics = internal_result.get('metrics', {})
                    internal_accuracy = internal_metrics.get('accuracy', 0)

                    # 模拟外部验证结果
                    external_accuracy = np.random.uniform(max(0, internal_accuracy - 0.1),
                                                        min(1, internal_accuracy + 0.05))
                    external_f1 = np.random.uniform(max(0, internal_metrics.get('f1', 0) - 0.1),
                                                  min(1, internal_metrics.get('f1', 0) + 0.05))
                    external_auc = np.random.uniform(max(0, internal_metrics.get('auc', 0) - 0.1),
                                                   min(1, internal_metrics.get('auc', 0) + 0.05))

                    performance_diff = external_accuracy - internal_accuracy

                    validation_results[model_name] = {
                        'internal_accuracy': internal_accuracy,
                        'external_accuracy': external_accuracy,
                        'performance_diff': performance_diff,
                        'external_f1': external_f1,
                        'external_auc': external_auc,
                        'internal_metrics': internal_metrics
                    }

                    # 记录结果
                    self._log_message(f"{model_name} 验证完成 - "
                                    f"内部准确率: {internal_accuracy:.3f}, "
                                    f"外部准确率: {external_accuracy:.3f}, "
                                    f"性能差异: {performance_diff:+.3f}")

                # 验证完成
                if self.is_validating:
                    if self.main_frame:
                        self.main_frame.after(0, lambda: self._validation_completed(validation_results))

            except Exception as e:
                if self.main_frame:
                    self.main_frame.after(0, lambda: self._validation_failed(str(e)))

        # 在后台线程中运行验证
        validation_thread = threading.Thread(target=validation)
        validation_thread.daemon = True
        validation_thread.start()

    def _log_message(self, message):
        """添加日志消息"""
        if hasattr(self, 'log_text'):
            timestamp = time.strftime("%H:%M:%S")
            log_entry = f"[{timestamp}] {message}\n"

            self.log_text.config(state='normal')
            self.log_text.insert('end', log_entry)
            self.log_text.see('end')
            self.log_text.config(state='disabled')

    def _clear_results_display(self):
        """清空结果显示"""
        # 清空结果表格
        if hasattr(self, 'results_tree'):
            for item in self.results_tree.get_children():
                self.results_tree.delete(item)

        # 重置进度条
        if hasattr(self, 'progress_widget'):
            self.progress_widget.set_progress(0)

    def _validation_completed(self, results):
        """验证完成处理"""
        self.validation_results = results
        self.is_validating = False

        # 更新UI状态
        self.status_label.config(text=f"✅ 外部验证完成！验证了 {len(results)} 个模型")
        self.validation_button.config(state='normal')
        self.stop_button.config(state='disabled')
        self.progress_widget.set_progress(100)

        # 更新结果表格
        self._update_results_table(results)

        # 记录完成信息
        self._log_message(f"外部验证完成！共验证 {len(results)} 个模型")

        # 分析验证结果
        self._analyze_validation_results(results)

        # 发布验证完成事件
        event_manager = get_event_manager()
        event_manager.publish(EventTypes.MODEL_TRAINED, {
            'results': results,
            'type': 'external_validation'
        })

    def _validation_failed(self, error_message):
        """验证失败处理"""
        self.is_validating = False
        self.status_label.config(text=f"❌ 外部验证失败: {error_message}")
        self.validation_button.config(state='normal')
        self.stop_button.config(state='disabled')

        self._log_message(f"外部验证失败: {error_message}")
        self.show_error("验证失败", f"外部验证过程中出现错误:\n{error_message}")

    def _update_results_table(self, results):
        """更新结果表格"""
        if not hasattr(self, 'results_tree'):
            return

        # 清空现有结果
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)

        # 添加新结果
        for model_name, result in results.items():
            values = (
                model_name,
                f"{result['internal_accuracy']:.3f}",
                f"{result['external_accuracy']:.3f}",
                f"{result['performance_diff']:+.3f}",
                f"{result['external_f1']:.3f}",
                f"{result['external_auc']:.3f}"
            )

            self.results_tree.insert('', 'end', values=values)

    def _analyze_validation_results(self, results):
        """分析验证结果"""
        if not results:
            return

        # 计算统计信息
        performance_diffs = [result['performance_diff'] for result in results.values()]
        avg_diff = np.mean(performance_diffs)
        std_diff = np.std(performance_diffs)

        # 分析结果
        if avg_diff > 0.05:
            self._log_message("⚠️ 警告: 外部验证性能显著高于内部验证，可能存在数据泄露")
        elif avg_diff < -0.1:
            self._log_message("⚠️ 警告: 外部验证性能显著低于内部验证，模型可能过拟合")
        else:
            self._log_message("✅ 验证结果良好: 内外部性能差异在合理范围内")

        self._log_message(f"平均性能差异: {avg_diff:+.3f} ± {std_diff:.3f}")

        # 找出最稳定的模型
        most_stable_model = min(results.keys(), key=lambda k: abs(results[k]['performance_diff']))
        self._log_message(f"最稳定模型: {most_stable_model} (性能差异: {results[most_stable_model]['performance_diff']:+.3f})")

    def _bind_events(self):
        """绑定事件"""
        event_manager = get_event_manager()
        event_manager.subscribe(EventTypes.DATA_LOADED, self._on_data_loaded)
        event_manager.subscribe(EventTypes.MODEL_TRAINED, self._on_model_trained)

    def _on_data_loaded(self, event_data):
        """数据加载事件处理"""
        try:
            if event_data and 'data' in event_data:
                self.current_data = event_data['data']

                # 更新状态
                rows, cols = self.current_data.shape
                self._log_message(f"内部训练数据: {rows} 行, {cols} 列")
                self.logger.info(f"外部验证模块已接收内部数据: {self.current_data.shape}")
        except Exception as e:
            self.logger.error(f"处理数据加载事件时出错: {e}")
            self._log_message(f"数据加载失败: {e}")

    def _on_model_trained(self, event_data):
        """模型训练完成事件处理"""
        try:
            if event_data and 'results' in event_data:
                # 检查是否是外部验证结果
                if event_data.get('type') == 'external_validation':
                    return  # 忽略外部验证结果，避免循环

                self.training_results = event_data['results']

                # 更新模型选择界面
                self._update_model_selection()

                # 更新状态
                model_count = len(self.training_results)
                self.status_label.config(text=f"✅ 已完成 {model_count} 个模型训练，可以开始外部验证")

                # 检查是否可以开始验证
                self._check_validation_ready()

                self._log_message(f"接收到 {model_count} 个训练完成的模型")
                self.logger.info(f"外部验证模块已接收训练结果: {model_count} 个模型")
        except Exception as e:
            self.logger.error(f"处理模型训练事件时出错: {e}")
            self._log_message(f"处理模型训练结果失败: {e}")

    def _update_model_selection(self):
        """更新模型选择界面"""
        if not self.training_results:
            return

        # 清空现有的复选框
        for widget in self.model_checkboxes_frame.winfo_children():
            widget.destroy()

        # 创建新的复选框
        self.model_vars = {}
        for model_name, result in self.training_results.items():
            var = tk.BooleanVar()
            self.model_vars[model_name] = var

            # 获取模型性能信息
            metrics = result.get('metrics', {})
            accuracy = metrics.get('accuracy', 0)
            f1 = metrics.get('f1', 0)

            # 创建复选框，显示模型名称和性能
            cb_text = f"{model_name} (准确率: {accuracy:.3f}, F1: {f1:.3f})"
            cb = tk.Checkbutton(self.model_checkboxes_frame, text=cb_text, variable=var)
            cb.pack(anchor='w', padx=10, pady=2)

        # 启用全选/取消全选按钮
        self.select_all_btn.config(state='normal')
        self.deselect_all_btn.config(state='normal')

        # 默认选择所有模型
        for var in self.model_vars.values():
            var.set(True)

    def clear_results(self):
        """清空所有结果"""
        self.validation_results = {}
        self.external_data = None

        # 清空结果显示
        self._clear_results_display()

        # 清空日志
        if hasattr(self, 'log_text'):
            self.log_text.config(state='normal')
            self.log_text.delete('1.0', 'end')
            self.log_text.insert('1.0', "外部验证日志将在这里显示...\n")
            self.log_text.config(state='disabled')

        # 重置文件路径
        if hasattr(self, 'file_path_var'):
            self.file_path_var.set('')

        # 重置数据信息
        if hasattr(self, 'data_info_label'):
            self.data_info_label.config(text="请选择外部验证数据文件")

        # 重置目标列选择
        if hasattr(self, 'target_column_combo'):
            self.target_column_combo['values'] = []
            self.target_column_combo.set('')

        # 重置模型选择
        if hasattr(self, 'model_checkboxes_frame'):
            for widget in self.model_checkboxes_frame.winfo_children():
                widget.destroy()

            factory = get_component_factory()
            self.no_models_label = factory.create_label(
                self.model_checkboxes_frame,
                text="请先完成模型训练",
                style='secondary'
            )
            self.no_models_label.pack(pady=10)

        # 重置状态
        if hasattr(self, 'status_label'):
            self.status_label.config(text="等待外部数据加载...")

        # 禁用按钮
        if hasattr(self, 'validation_button'):
            self.validation_button.config(state='disabled')
        if hasattr(self, 'select_all_btn'):
            self.select_all_btn.config(state='disabled')
        if hasattr(self, 'deselect_all_btn'):
            self.deselect_all_btn.config(state='disabled')

        self._log_message("所有结果已清空")

    def get_validation_results(self) -> Dict[str, Any]:
        """获取验证结果"""
        return self.validation_results.copy()

    def is_validation_active(self) -> bool:
        """检查是否正在进行验证"""
        return self.is_validating
