#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
机器学习平台命令行工具
提供完整的命令行界面支持
"""

import argparse
import sys
import os
import logging
from pathlib import Path
from typing import List, Optional, Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.config_manager import get_config_manager
from core.model_manager import get_model_manager
from core.session_manager import get_session_manager
from utils.data_loader import get_data_loader
from utils.error_handler import get_error_handler
from utils.plot_manager import get_plot_manager
from utils.report_generator import get_report_generator
from utils.delong_test import perform_delong_comparison
from algorithms import MODEL_NAMES, MODEL_DISPLAY_NAMES


class MLPlatformCLI:
    """机器学习平台CLI类"""
    
    def __init__(self):
        """初始化CLI"""
        self.config_manager = get_config_manager()
        self.model_manager = get_model_manager()
        self.session_manager = get_session_manager()
        self.data_loader = get_data_loader()
        self.error_handler = get_error_handler()
        self.plot_manager = get_plot_manager()
        self.report_generator = get_report_generator()
        
        # 设置日志
        self._setup_logging()
        
        self.logger = logging.getLogger(__name__)
        self.logger.info("机器学习平台CLI初始化完成")
    
    def _setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout),
                logging.FileHandler('ml_platform_cli.log', encoding='utf-8')
            ]
        )
    
    def create_parser(self) -> argparse.ArgumentParser:
        """创建命令行参数解析器"""
        parser = argparse.ArgumentParser(
            description='机器学习平台命令行工具',
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
使用示例:
  # 训练所有模型
  python -m cli.main --data data.csv --target target --mode train --model All
  
  # 训练特定模型
  python -m cli.main --data data.csv --target target --mode train --model RandomForest,XGBoost
  
  # 生成可视化
  python -m cli.main --data data.csv --target target --mode plot --model XGBoost
  
  # 生成报告
  python -m cli.main --data data.csv --target target --mode report --model All
  
  # DeLong检验
  python -m cli.main --data data.csv --target target --mode delong --model RandomForest,XGBoost
  
  # 一键完整分析
  python -m cli.main --data data.csv --target target --mode pipeline
            """
        )
        
        # 基本参数
        parser.add_argument('--data', '-d', type=str, required=True,
                          help='数据文件路径 (CSV/Excel)')
        
        parser.add_argument('--target', '-t', type=str, required=True,
                          help='目标列名称')
        
        parser.add_argument('--mode', '-m', type=str, required=True,
                          choices=['train', 'plot', 'report', 'delong', 'pipeline'],
                          help='运行模式')
        
        parser.add_argument('--model', type=str, default='All',
                          help='模型名称，多个模型用逗号分隔，或使用"All"表示所有模型')
        
        # 输出参数
        parser.add_argument('--output', '-o', type=str, default='output',
                          help='输出目录路径')
        
        parser.add_argument('--session-name', type=str,
                          help='会话名称')
        
        # 训练参数
        parser.add_argument('--test-size', type=float, default=0.2,
                          help='测试集比例 (默认: 0.2)')
        
        parser.add_argument('--cv-folds', type=int, default=5,
                          help='交叉验证折数 (默认: 5)')
        
        parser.add_argument('--random-state', type=int, default=42,
                          help='随机种子 (默认: 42)')
        
        # 预处理参数
        parser.add_argument('--no-preprocessing', action='store_true',
                          help='禁用数据预处理')
        
        parser.add_argument('--no-tuning', action='store_true',
                          help='禁用超参数调优')
        
        # DeLong检验参数
        parser.add_argument('--alpha', type=float, default=0.05,
                          help='DeLong检验显著性水平 (默认: 0.05)')
        
        # GPU参数
        parser.add_argument('--gpu', action='store_true',
                          help='启用GPU加速 (如果可用)')
        
        # 其他参数
        parser.add_argument('--verbose', '-v', action='store_true',
                          help='详细输出')
        
        parser.add_argument('--quiet', '-q', action='store_true',
                          help='静默模式')
        
        parser.add_argument('--strict-reproducibility', action='store_true',
                          help='严格复现模式')
        
        return parser
    
    def parse_models(self, model_str: str) -> List[str]:
        """解析模型参数"""
        if model_str.lower() == 'all':
            return MODEL_NAMES if MODEL_NAMES else ['RandomForest', 'XGBoost', 'LightGBM']
        else:
            models = [m.strip() for m in model_str.split(',')]
            # 验证模型名称
            valid_models = []
            for model in models:
                if model in MODEL_NAMES or model in ['RandomForest', 'XGBoost', 'LightGBM']:
                    valid_models.append(model)
                else:
                    self.logger.warning(f"未知模型: {model}")
            return valid_models
    
    def setup_environment(self, args):
        """设置运行环境"""
        # 设置随机种子
        import random
        import numpy as np
        
        random.seed(args.random_state)
        np.random.seed(args.random_state)
        
        if args.strict_reproducibility:
            # 严格复现模式
            os.environ['PYTHONHASHSEED'] = str(args.random_state)
            
            try:
                import tensorflow as tf
                tf.random.set_seed(args.random_state)
            except ImportError:
                pass
            
            try:
                import torch
                torch.manual_seed(args.random_state)
                if torch.cuda.is_available():
                    torch.cuda.manual_seed_all(args.random_state)
            except ImportError:
                pass
        
        # 设置日志级别
        if args.quiet:
            logging.getLogger().setLevel(logging.ERROR)
        elif args.verbose:
            logging.getLogger().setLevel(logging.DEBUG)
        
        # 创建输出目录
        output_dir = Path(args.output)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        self.logger.info(f"输出目录: {output_dir.absolute()}")
    
    def load_data(self, args) -> tuple:
        """加载数据"""
        self.logger.info(f"加载数据: {args.data}")
        
        try:
            data = self.data_loader.load_data(args.data)
            
            if args.target not in data.columns:
                raise ValueError(f"目标列 '{args.target}' 不存在于数据中")
            
            X = data.drop(columns=[args.target])
            y = data[args.target]
            
            self.logger.info(f"数据形状: {data.shape}")
            self.logger.info(f"特征数量: {X.shape[1]}")
            self.logger.info(f"目标列: {args.target}")
            self.logger.info(f"类别分布: {y.value_counts().to_dict()}")
            
            return X, y
            
        except Exception as e:
            self.logger.error(f"数据加载失败: {e}")
            sys.exit(1)
    
    def create_session(self, args) -> str:
        """创建训练会话"""
        session_name = args.session_name or f"CLI_Session_{Path(args.data).stem}"
        description = f"CLI训练会话 - 数据: {args.data}, 模式: {args.mode}"
        
        session = self.session_manager.create_session(session_name, description)
        self.logger.info(f"创建会话: {session.session_name} ({session.session_id})")
        
        return session.session_id
    
    def train_models(self, args, X, y, models: List[str]) -> Dict[str, Any]:
        """训练模型"""
        self.logger.info(f"开始训练 {len(models)} 个模型")
        
        results = {}
        
        for i, model_name in enumerate(models, 1):
            self.logger.info(f"[{i}/{len(models)}] 训练模型: {model_name}")
            
            try:
                # 这里可以调用算法模块进行训练
                result = self.model_manager.train_model(
                    model_name, X, y,
                    test_size=args.test_size,
                    cv_folds=args.cv_folds,
                    enable_preprocessing=not args.no_preprocessing,
                    enable_tuning=not args.no_tuning,
                    random_state=args.random_state
                )
                
                results[model_name] = result
                
                accuracy = result.get('accuracy', 0)
                training_time = result.get('training_time', 0)
                
                self.logger.info(f"模型 {model_name} 训练完成 - "
                               f"准确率: {accuracy:.4f}, 用时: {training_time:.2f}秒")
                
            except Exception as e:
                self.logger.error(f"模型 {model_name} 训练失败: {e}")
                continue
        
        self.logger.info(f"训练完成，成功训练 {len(results)} 个模型")
        return results
    
    def generate_plots(self, args, results: Dict[str, Any]):
        """生成可视化图表"""
        self.logger.info("生成可视化图表")
        
        output_dir = Path(args.output) / 'plots'
        output_dir.mkdir(parents=True, exist_ok=True)
        
        for model_name, result in results.items():
            try:
                self.logger.info(f"生成 {model_name} 的图表")
                
                y_true = result.get('y_true') or result.get('y_test')
                y_pred_proba = result.get('y_pred_proba')
                y_pred = result.get('y_pred')
                
                # ROC曲线
                if y_true is not None and y_pred_proba is not None:
                    fig = self.plot_manager.plot_roc_curve(
                        y_true, y_pred_proba, model_name,
                        save_path=str(output_dir / f'{model_name}_roc.png')
                    )
                    plt.close(fig)
                
                # 混淆矩阵
                if y_true is not None and y_pred is not None:
                    fig = self.plot_manager.plot_confusion_matrix(
                        y_true, y_pred, model_name,
                        save_path=str(output_dir / f'{model_name}_confusion_matrix.png')
                    )
                    plt.close(fig)
                
                # 特征重要性
                model = result.get('model')
                feature_names = result.get('feature_names')
                
                if model and hasattr(model, 'feature_importances_') and feature_names:
                    fig = self.plot_manager.plot_feature_importance(
                        feature_names, model.feature_importances_, model_name,
                        save_path=str(output_dir / f'{model_name}_feature_importance.png')
                    )
                    plt.close(fig)
                
            except Exception as e:
                self.logger.error(f"生成 {model_name} 图表失败: {e}")
        
        self.logger.info(f"图表已保存到: {output_dir}")
    
    def generate_reports(self, args, results: Dict[str, Any]):
        """生成报告"""
        self.logger.info("生成性能报告")
        
        output_dir = Path(args.output) / 'reports'
        output_dir.mkdir(parents=True, exist_ok=True)
        
        try:
            # 生成模型比较报告
            if len(results) > 1:
                report_path = output_dir / 'model_comparison_report.html'
                self.report_generator.generate_comparison_report(
                    results, str(report_path)
                )
                self.logger.info(f"模型比较报告已生成: {report_path}")
            
            # 生成单模型报告
            for model_name, result in results.items():
                report_path = output_dir / f'{model_name}_report.html'
                self.report_generator.generate_single_model_report(
                    model_name, result, str(report_path)
                )
                self.logger.info(f"{model_name} 报告已生成: {report_path}")
            
        except Exception as e:
            self.logger.error(f"生成报告失败: {e}")
    
    def perform_delong_test(self, args, results: Dict[str, Any]):
        """执行DeLong检验"""
        self.logger.info("执行DeLong检验")
        
        if len(results) < 2:
            self.logger.warning("DeLong检验需要至少2个模型")
            return
        
        try:
            # 准备数据
            model_data = {}
            for model_name, result in results.items():
                y_true = result.get('y_true') or result.get('y_test')
                y_pred_proba = result.get('y_pred_proba')
                
                if y_true is not None and y_pred_proba is not None:
                    model_data[model_name] = {
                        'y_true': y_true,
                        'y_scores': y_pred_proba
                    }
            
            if len(model_data) < 2:
                self.logger.warning("没有足够的模型数据进行DeLong检验")
                return
            
            # 执行检验
            delong_results = perform_delong_comparison(model_data, args.alpha)
            
            # 保存结果
            output_dir = Path(args.output) / 'delong'
            output_dir.mkdir(parents=True, exist_ok=True)
            
            results_path = output_dir / 'delong_results.json'
            
            import json
            save_results = delong_results.copy()
            if 'comparison_matrix' in save_results:
                save_results['comparison_matrix'] = save_results['comparison_matrix'].tolist()
            if 'p_value_matrix' in save_results:
                save_results['p_value_matrix'] = save_results['p_value_matrix'].tolist()
            
            with open(results_path, 'w', encoding='utf-8') as f:
                json.dump(save_results, f, indent=2, ensure_ascii=False)
            
            # 输出摘要
            summary = delong_results['summary']
            print(f"\n=== DeLong检验结果 ===")
            print(f"比较模型数量: {len(summary['model_names'])}")
            print(f"总比较次数: {summary['total_comparisons']}")
            print(f"显著差异比较: {summary['significant_comparisons']}")
            print(f"显著性水平: {summary['alpha']}")
            
            for result in delong_results['pairwise_results']:
                significance = "显著" if result['is_significant'] else "不显著"
                print(f"{result['model1']} vs {result['model2']}: "
                      f"AUC差异={result['auc_diff']:.4f}, p={result['p_value']:.4f}, {significance}")
            
            self.logger.info(f"DeLong检验结果已保存到: {results_path}")
            
        except Exception as e:
            self.logger.error(f"DeLong检验失败: {e}")
    
    def run_pipeline(self, args):
        """运行完整分析流水线"""
        self.logger.info("开始完整分析流水线")
        
        # 1. 加载数据
        X, y = self.load_data(args)
        
        # 2. 创建会话
        session_id = self.create_session(args)
        
        # 3. 解析模型
        models = self.parse_models(args.model)
        
        # 4. 训练模型
        results = self.train_models(args, X, y, models)
        
        if not results:
            self.logger.error("没有成功训练的模型")
            return
        
        # 5. 生成可视化
        self.generate_plots(args, results)
        
        # 6. 生成报告
        self.generate_reports(args, results)
        
        # 7. DeLong检验（如果有多个模型）
        if len(results) > 1:
            self.perform_delong_test(args, results)
        
        # 8. 保存会话
        self.session_manager.save_session()
        
        self.logger.info("完整分析流水线执行完成")
        print(f"\n✅ 分析完成！结果已保存到: {Path(args.output).absolute()}")
    
    def run(self, args):
        """运行CLI"""
        try:
            # 设置环境
            self.setup_environment(args)
            
            if args.mode == 'pipeline':
                self.run_pipeline(args)
            else:
                # 加载数据
                X, y = self.load_data(args)
                
                # 创建会话
                session_id = self.create_session(args)
                
                # 解析模型
                models = self.parse_models(args.model)
                
                if args.mode == 'train':
                    results = self.train_models(args, X, y, models)
                    self.session_manager.save_session()
                    print(f"✅ 训练完成！成功训练 {len(results)} 个模型")
                
                elif args.mode == 'plot':
                    # 需要先训练或加载模型
                    results = self.train_models(args, X, y, models)
                    self.generate_plots(args, results)
                    print(f"✅ 图表生成完成！")
                
                elif args.mode == 'report':
                    # 需要先训练或加载模型
                    results = self.train_models(args, X, y, models)
                    self.generate_reports(args, results)
                    print(f"✅ 报告生成完成！")
                
                elif args.mode == 'delong':
                    # 需要先训练或加载模型
                    results = self.train_models(args, X, y, models)
                    self.perform_delong_test(args, results)
                    print(f"✅ DeLong检验完成！")
        
        except KeyboardInterrupt:
            self.logger.info("用户中断执行")
            print("\n❌ 执行被用户中断")
            sys.exit(1)
        
        except Exception as e:
            self.logger.error(f"执行失败: {e}")
            print(f"\n❌ 执行失败: {e}")
            sys.exit(1)


def main():
    """主函数"""
    cli = MLPlatformCLI()
    parser = cli.create_parser()
    args = parser.parse_args()
    
    cli.run(args)


if __name__ == '__main__':
    main()
